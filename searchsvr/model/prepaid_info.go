package model

import (
	"context"
	"fmt"
	"sort"
	"strconv"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/polygon"
)

type PrepaidSearchES struct {
	PrepaidId    *string `json:"prepaid_id,omitempty"`
	StoreId      *string `json:"store_id,omitempty"`
	DishId       *string `json:"dish_id,omitempty"`
	MaxDishPrice *uint64 `json:"max_dish_price,omitempty"` // 最大菜品价格
}

func (p *PrepaidSearchES) GetPrepaidId() uint64 {
	if p != nil && p.PrepaidId != nil {
		val, _ := strconv.Atoi(*p.PrepaidId)
		return uint64(val)
	}
	return 0
}

func (p *PrepaidSearchES) GetStoreId() uint64 {
	if p != nil && p.StoreId != nil {
		val, _ := strconv.Atoi(*p.StoreId)
		return uint64(val)
	}
	return 0
}

func (p *PrepaidSearchES) GetDishId() uint64 {
	if p != nil && p.DishId != nil {
		val, _ := strconv.Atoi(*p.DishId)
		return uint64(val)
	}
	return 0
}
func (p *PrepaidSearchES) GetMaxDishPrice() uint64 {
	if p != nil && p.MaxDishPrice != nil {
		return *p.MaxDishPrice
	}
	return 0
}

type PrepaidInfo struct {
	PrepaidId uint64 `json:"prepaid_id"`
	StoreId   uint64 `json:"store_id"`
	DishId    uint64 `json:"dish_id"`

	// 正排信息
	PrepaidMeta *o2oalgo.Item
	StoreMeta   *o2oalgo.Store
	DishMeta    *o2oalgo.Dish
	ItemQuota   uint64 `json:"item_quota"`  // 总库存,正排返回是list,实际只有1个
	BrandId     uint64 `json:"brand_id"`    // 品牌ID
	MerchantId  uint64 `json:"merchant_id"` // 商户ID

	// 召回信息
	ESScore        float64  `json:"es_score"`        // ES score, used for ranking
	MaxDishPrice   uint64   `json:"max_dish_price"`  // 最大菜品价格
	RecallPriority int      `json:"recall_priority"` // 召回优先级, 用于区分召回优先级
	EsExplains     []string `json:"es_explains"`     // ES explain info

	// 特征信息
	UserHistorySalesVolume uint64                 `json:"user_history_sales_volume"` // 用户购买此券的数量，数据从FS查询，用于landing page用户购买数量过滤
	PrepaidYearSales       uint64                 `json:"prepaid_year_sales"`        // prepaid年度销量，data统计后同步到FS, 用于forBag 排序
	PrepaidItemSoldCount   uint64                 `json:"prepaid_item_sold_count"`   // prepaid 销量，统计口径未知。算法生成，从data server获取，支持配置，需要归一化，用于landing page打分因子
	FeatureParams          map[string]interface{} `json:"feature_params"`            // 特征参数, 预留字段. 且其中所有特征都是float64类型

	// 排序信息
	ExactMatch      float64 `json:"exact_match"`      // Exact match score, used for ranking. SKU Name/Sku ID fully match
	PrepaidDiscount float64 `json:"prepaid_discount"` // 折扣, landing page 和bag page都需要,且计算的菜品价格有点不一样，具体看详细计算函数
	IsSoldOut       int     `json:"is_sold_out"`      // 是否售罄,ItemQuota-SalesVolume > 0 ? 0-未售罄 : 1-售罄
	Distance        float64 `json:"distance"`
	FusionScore     float64 `json:"fusion_score"` // Fusion score, used for ranking

	// 其他信息
	ItemSceneType         foodalgo_search.ItemSceneType `json:"item_scene_type"` // 0-normal, 1-few result item
	PolygonKey            string                        `json:"polygon_key"`     // 用于存储多边形的key, 例如: "{store_id}_{delivery_distance}", 例如: "123456_1000"
	StorePolygon          *polygon.Polygon              `json:"-"`
	PosBeforeBrandShuffle int64                         `json:"pos_before_brand_shuffle"` // 用于记录在品牌洗牌前的排序位置, 例如: 1,2,3,4,5,6,7,8,9,10
	PosAfterBrandShuffle  int64                         `json:"pos_after_brand_shuffle"`  // 用于记录在品牌洗牌后的排序位置, 例如: 1,2,3,4,5,6,7,8,9,10
}

const FloatZero = float64(0.0)

// InitFeatureParams 初始化特征参数，所有显示定义的因子要在这里统一赋默认值
func (info *PrepaidInfo) InitFeatureParams() {
	if info == nil {
		return
	}
	if info.FeatureParams == nil {
		info.FeatureParams = make(map[string]interface{})
	}
	// 所有显示定义的因子要在这里统一赋默认值
	info.FeatureParams["user_brand_purchase"] = FloatZero
	info.FeatureParams["discount_score"] = FloatZero
	info.FeatureParams["dist_score"] = FloatZero
	info.FeatureParams["rel_score"] = FloatZero
	info.FeatureParams["sale_score"] = FloatZero
}

// 所有配置定义的因子要在这里统一赋默认值
func (info *PrepaidInfo) SetFeatureParamsDefault(key string, value float64) {
	if info == nil {
		return
	}
	if info.FeatureParams == nil {
		info.FeatureParams = make(map[string]interface{})
	}
	if _, ok := info.FeatureParams[key]; !ok {
		info.FeatureParams[key] = value
	}
}

func (info *PrepaidInfo) GetFeatureParamsFloat(ctx context.Context, key string) float64 {
	if info == nil {
		return 0.0
	}
	if info.FeatureParams == nil {
		return 0.0
	}
	if val, exist := info.FeatureParams[key]; exist {
		if floatVal, ok := val.(float64); ok {
			return floatVal
		} else {
			logkit.FromContext(ctx).Error("GetFeatureParamsFloat Value is not float64",
				logkit.String("key", key), logkit.Any("value", val), logkit.String("type", fmt.Sprintf("%T", val)))
		}
	}
	return 0.0
}

type PrepaidInfos []*PrepaidInfo

func (infos PrepaidInfos) PrepaidMap() map[uint64]*PrepaidInfo {
	idSet := make(map[uint64]*PrepaidInfo, len(infos))
	for _, info := range infos {
		if info == nil || info.PrepaidId == 0 {
			continue
		}
		idSet[info.PrepaidId] = info
	}
	return idSet
}

func (infos PrepaidInfos) DistinctPrepaidIds() []uint64 {
	idSet := make(map[uint64]struct{}, len(infos))
	ids := make([]uint64, 0, len(infos))
	for _, info := range infos {
		if info == nil || info.PrepaidId == 0 {
			continue
		}
		if _, ok := idSet[info.PrepaidId]; ok {
			continue
		}
		idSet[info.PrepaidId] = struct{}{}
		ids = append(ids, info.PrepaidId)
	}
	return ids
}

func (infos PrepaidInfos) PrepaidIdsSet() map[uint64]struct{} {
	idSet := make(map[uint64]struct{}, len(infos))
	for _, info := range infos {
		if info == nil || info.PrepaidId == 0 {
			continue
		}
		if _, ok := idSet[info.PrepaidId]; ok {
			continue
		}
		idSet[info.PrepaidId] = struct{}{}
	}
	return idSet
}

func (infos PrepaidInfos) DistinctStoreIds() []uint64 {
	idSet := make(map[uint64]struct{}, len(infos))
	ids := make([]uint64, 0, len(infos))
	for _, info := range infos {
		if info == nil || info.StoreId == 0 {
			continue
		}
		if _, ok := idSet[info.StoreId]; ok {
			continue
		}
		idSet[info.StoreId] = struct{}{}
		ids = append(ids, info.StoreId)
	}
	return ids
}

func (infos PrepaidInfos) DistinctDishIds() []uint64 {
	idSet := make(map[uint64]struct{}, len(infos))
	ids := make([]uint64, 0, len(infos))
	for _, info := range infos {
		if info == nil || info.DishId == 0 {
			continue
		}
		if _, ok := idSet[info.DishId]; ok {
			continue
		}
		idSet[info.DishId] = struct{}{}
		ids = append(ids, info.DishId)
	}
	return ids
}

// unavailable > exact match > > PrepaidYearSales > discount > prepaid_id
func (infos PrepaidInfos) SortPrepaidForBag() PrepaidInfos {
	sort.Slice(infos, func(i, j int) bool {
		if infos[i].IsSoldOut != infos[j].IsSoldOut {
			return infos[i].IsSoldOut < infos[j].IsSoldOut // IsSoldOut
		}
		if infos[i].ExactMatch != infos[j].ExactMatch {
			return infos[i].ExactMatch > infos[j].ExactMatch // ExactMatch
		}
		if infos[i].PrepaidYearSales != infos[j].PrepaidYearSales {
			return infos[i].PrepaidYearSales > infos[j].PrepaidYearSales // PrepaidYearSales
		}
		if !acc.Equal(infos[i].PrepaidDiscount, infos[j].PrepaidDiscount) {
			return infos[i].PrepaidDiscount > infos[j].PrepaidDiscount // 折扣降序
		}
		return infos[i].PrepaidId > infos[j].PrepaidId // ID降序, 兜底
	})
	return infos
}

// distance > prepaid_id
func (infos PrepaidInfos) SortByDistance() PrepaidInfos {
	sort.Slice(infos, func(i, j int) bool {
		if !acc.Equal(infos[i].Distance, infos[j].Distance) {
			return infos[i].Distance < infos[j].Distance // IsSoldOut
		}
		return infos[i].PrepaidId > infos[j].PrepaidId // ID降序, 兜底
	})
	return infos
}

// PrepaidDiscount > prepaid_id
func (infos PrepaidInfos) SortByPrepaidDiscount() PrepaidInfos {
	sort.Slice(infos, func(i, j int) bool {
		if !acc.Equal(infos[i].PrepaidDiscount, infos[j].PrepaidDiscount) {
			return infos[i].PrepaidDiscount > infos[j].PrepaidDiscount // PrepaidDiscount
		}
		return infos[i].PrepaidId > infos[j].PrepaidId // ID降序, 兜底
	})
	return infos
}

// FusionScore > prepaid_id
func (infos PrepaidInfos) SortByFusionScore() PrepaidInfos {
	sort.Slice(infos, func(i, j int) bool {
		if !acc.Equal(infos[i].FusionScore, infos[j].FusionScore) {
			return infos[i].FusionScore > infos[j].FusionScore // FusionScore
		}
		return infos[i].PrepaidId > infos[j].PrepaidId // ID降序, 兜底
	})
	return infos
}
