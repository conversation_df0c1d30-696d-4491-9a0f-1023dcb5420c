package mlplatform

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	microclient "git.garena.com/shopee/digital-purchase/recsys/common/micro/client"
	"git.garena.com/shopee/digital-purchase/recsys/common/micro/plugin"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/spexcommon"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/common_client"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"go.uber.org/zap"

	spex "git.garena.com/shopee/o2o-intelligence/common/common-lib/spex_client_gray"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/platform/golang_splib/client/callopt"
	"github.com/micro/go-micro/client"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

const Prodict_PROXY_NAME = "o2oalgo_predictorproxysearch"

var PredictorClient predictor.PredictService
var SpexClient predictor.Client

func InitPredictService(microClientConfig *common_client.MicroClientConfig) error {
	config := &microclient.MicroClientConfig{
		Region:       microClientConfig.Region,
		Env:          microClientConfig.Env,
		ZkAddress:    microClientConfig.ZkAddress,
		ConnPoolSize: microClientConfig.ConnPoolSize,
	}
	microClient := microclient.NewMicroClient(config)
	microClient.AddPlugin(plugin.NewRecoverClientPlugin())
	microClient.Init()
	predictorServerFullName := microClient.GetFullServiceName(Prodict_PROXY_NAME)
	PredictorClient = predictor.NewPredictService(predictorServerFullName, microClient.GetService().Client())
	var err error
	if cid.IsVN() {
		SpexClient, err = predictor.NewVNSpexClient(spexcommon.DefaultClientOptions...)
	} else {
		SpexClient, err = predictor.NewSpexClient(spexcommon.DefaultClientOptions...)
	}
	if err != nil {
		logkit.Fatal("failed to init predict spex client")
	}
	return err
}
func Predict(ctx context.Context, in *predictor.PredictReq, isDebug bool, opts ...client.CallOption) (*predictor.PredictRsp, bool, error) {
	timeOut := apollo.SearchApolloCfg.ClientTimeOutConfig.PredictClientTimeOut
	if timeOut == 0 {
		timeOut = 200
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeOut)*time.Millisecond)
	defer cancel()
	//if isDebug {
	//	in.DebugFlag = predictor.DebugLevel_DEBUGLV_HIGHEST // debug 标识，用于模型平台定位case
	//}
	if spex.DefaultSwitchFunc(ctx, "o2oalgo.ml_common.predictorproxyfoodsearch", "predict") { // grey related
		resp, err := PredictorClient.Predict(ctx, in, opts...)
		return resp, false, err
	}
	// spex无option
	resp, err := SpexClient.Predict(ctx, in)
	return resp, true, err
}

func PredictAck(ctx context.Context, in *predictor.PreAckReq, ackIpAddr, ackIpInstance string, isDebug bool) (*predictor.PreAckRsp, error) {
	timeOut := apollo.SearchApolloCfg.ClientTimeOutConfig.PredictAckClientTimeOut
	if timeOut == 0 {
		timeOut = 200
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeOut)*time.Millisecond)
	defer cancel()
	//if isDebug {
	//	in.DebugFlag = predictor.DebugLevel_DEBUGLV_HIGHEST // debug 标识，用于模型平台定位case
	//}
	if len(ackIpInstance) > 0 { // grey related
		return SpexClient.PreAck(ctx, in, callopt.WithInstanceID(ackIpInstance))
	}
	return PredictorClient.PreAck(ctx, in, client.WithAddress(ackIpAddr))
}

// 后续新增通用日志及上报， 替换Predict
func PredictV2(ctx context.Context, in *predictor.PredictReq, modelName string, isDebug bool) (*predictor.PredictRsp, error) {
	timeOut := apollo.SearchApolloCfg.ClientTimeOutConfig.PredictClientTimeOut
	if timeOut == 0 {
		timeOut = 200
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeOut)*time.Millisecond)
	defer cancel()

	startTime := time.Now()
	errCode := "failed"
	defer func() {
		metric_reporter.ReportClientRequestError(1, "predict-"+modelName, errCode)
	}()
	resp, err := SpexClient.Predict(ctx, in)
	logkit.FromContext(ctx).Info("cost time log", zap.String("method", "predict-"+modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "predict-"+modelName)
	if isDebug {
		reqStr, _ := json.Marshal(in)
		rspStr, _ := json.Marshal(resp)
		logkit.FromContext(ctx).Info("PredictV2 doPredict", logkit.String("modelName", modelName), logkit.String("req", string(reqStr)), logkit.String("rsp", string(rspStr)))
	}
	if err != nil {
		logkit.FromContext(ctx).Error("predict error", zap.Error(err), logkit.String("modelName", modelName), zap.Any("resp", resp))
		return nil, err
	}
	if resp == nil {
		logkit.FromContext(ctx).Error("failed to predict, empty resp", logkit.Any("rsp is ", "nil"), logkit.String("modelName", modelName))
		return nil, errors.New("predict resp is nil")
	}
	if resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict, with err code", logkit.String("modelName", modelName), logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return nil, errors.New(resp.GetErrdesc())
	}
	errCode = "0"
	return resp, err
}
