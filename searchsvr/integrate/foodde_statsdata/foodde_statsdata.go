package foodde_statsdata

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/digital-purchase/recsys/common/util"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodde_statsdata"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/spex"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"go.uber.org/zap"
)

type FooddeStatsDataClient struct {
	rpcClient foodde_statsdata.Client
}

var statsDataClient *FooddeStatsDataClient

func InitFooddeStatsDataClient() {
	client, err := spex.NewClient(foodde_statsdata.NewClient)
	if err != nil {
		logkit.Error("init spex client fail, check spexcommon.InitClient(), panic now!")
		logkit.Fatal("panic!")
	}
	statsDataClient = &FooddeStatsDataClient{
		rpcClient: client,
	}
}

func GetFooddeStatsDataClient() *FooddeStatsDataClient {
	return statsDataClient
}

func (dc *FooddeStatsDataClient) GetPrepaidSKUBuyerHistoryData(ctx context.Context, traceInfo *traceinfo.TraceInfo, uid uint64, prepaidIDs []uint64) (map[uint64]uint64, error) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingPrepaidSKUBuyerHistoryData, time.Since(pt))
	}()
	result := make(map[uint64]uint64, len(prepaidIDs))
	if uid == 0 {
		return result, nil
	}
	// 开始批量 RPC 调用
	batchSize := 200
	batchCount := len(prepaidIDs) / batchSize
	if len(prepaidIDs)%batchSize > 0 {
		batchCount += 1
	}
	wg := sync.WaitGroup{}
	wg.Add(batchCount)

	// 注意这里直接声明长度，所以如果有超时，里面有指针是nil，后续读取需要过滤掉
	prepaidSKUBuyerHistoryDataResList := make([]*foodde_statsdata.GetPrepaidSKUBuyerHistoryDataRes, batchCount)
	for i := 0; i < batchCount; i++ {
		var tempIDs []uint64
		if i+1 < batchCount {
			tempIDs = prepaidIDs[i*batchSize : (i+1)*batchSize]
		} else {
			tempIDs = prepaidIDs[i*batchSize:]
		}
		go func(temp []uint64, index int) {
			defer wg.Done()
			defer func() {
				if e := recover(); e != nil {
					logkit.Error("GetPrepaidSKUBuyerHistoryData panic", logkit.Any("err", e))
					metric_reporter.ReportCounter(metric_reporter.ReportServicePanicMetricName, 1, reporter.Label{
						Key: "method",
						Val: "GetPrepaidSKUBuyerHistoryData",
					})
				}
			}()
			req := &foodde_statsdata.GetPrepaidSKUBuyerHistoryDataReq{
				PrepaidSkuIds: temp,
			}
			if cid.IsVN() {
				req.NowUid = uid
			} else {
				req.ShopeeUid = uid
			}
			ctx, cancel := util.CtxWithTimeoutMs(ctx, 1000) // 设置超时时间为1000毫秒
			defer cancel()
			t1 := time.Now()
			resp, err := dc.rpcClient.GetPrepaidSKUBuyerHistoryData(ctx, req)
			metric_reporter2.ReportDurationAndQPS(time.Since(t1), metric_reporter2.SearchReportTypeRpc, "", "", "GetPrepaidSKUBuyerHistoryData")
			if err != nil {
				logkit.FromContext(ctx).Error("GetPrepaidSKUBuyerHistoryData fail", zap.Error(err))
				traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
				return
			}
			prepaidSKUBuyerHistoryDataResList[index] = resp
		}(tempIDs, i)
	}
	wg.Wait()
	// 从RPC中读取数据
	for _, rsp := range prepaidSKUBuyerHistoryDataResList {
		if rsp == nil {
			continue
		}
		for _, each := range rsp.GetSoldInfo() {
			if each == nil {
				continue
			}
			result[each.GetPrepaidSkuId()] = each.GetSkuSoldNum()
		}
	}
	return result, nil
}

func (dc *FooddeStatsDataClient) GetPrepaidSkuHistorySoldData(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaidIDs []uint64) (map[uint64]uint64, error) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingPrepaidSkuHistorySoldData, time.Since(pt))
	}()
	result := make(map[uint64]uint64, len(prepaidIDs))
	if len(prepaidIDs) == 0 {
		return result, nil
	}
	// 开始批量 RPC 调用
	batchSize := 200
	batchCount := len(prepaidIDs) / batchSize
	if len(prepaidIDs)%batchSize > 0 {
		batchCount += 1
	}
	wg := sync.WaitGroup{}
	wg.Add(batchCount)

	// 注意这里直接声明长度，所以如果有超时，里面有指针是nil，后续读取需要过滤掉
	prepaidSkuHistorySoldDataRspList := make([]*foodde_statsdata.GetPrepaidSkuHistorySoldDataRes, batchCount)
	for i := 0; i < batchCount; i++ {
		var tempIDs []uint64
		if i+1 < batchCount {
			tempIDs = prepaidIDs[i*batchSize : (i+1)*batchSize]
		} else {
			tempIDs = prepaidIDs[i*batchSize:]
		}
		go func(temp []uint64, index int) {
			defer wg.Done()
			defer func() {
				if e := recover(); e != nil {
					logkit.Error("GetPrepaidSkuHistorySoldData panic", logkit.Any("err", e))
					metric_reporter.ReportCounter(metric_reporter.ReportServicePanicMetricName, 1, reporter.Label{
						Key: "method",
						Val: "GetPrepaidSkuHistorySoldData",
					})
				}
			}()
			req := &foodde_statsdata.GetPrepaidSkuHistorySoldDataReq{
				PrepaidSkuIds: temp,
			}
			ctx, cancel := util.CtxWithTimeoutMs(ctx, 1000) // 设置超时时间为1000毫秒
			defer cancel()
			t1 := time.Now()
			resp, err := dc.rpcClient.GetPrepaidSkuHistorySoldData(ctx, req)
			metric_reporter2.ReportDurationAndQPS(time.Since(t1), metric_reporter2.SearchReportTypeRpc, "", "", "GetPrepaidSkuHistorySoldData")
			if err != nil {
				logkit.FromContext(ctx).Error("GetPrepaidSkuHistorySoldData fail", zap.Error(err))
				traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
				return
			}
			prepaidSkuHistorySoldDataRspList[index] = resp
		}(tempIDs, i)
	}
	wg.Wait()
	// 从RPC中读取数据
	for _, rsp := range prepaidSkuHistorySoldDataRspList {
		if rsp == nil {
			continue
		}
		for _, each := range rsp.GetSkus() {
			if each == nil {
				continue
			}
			result[each.GetPrepaidSkuId()] = each.GetSoldNum()
		}
	}
	return result, nil
}
