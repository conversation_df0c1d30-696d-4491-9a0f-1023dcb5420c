package data_server

import (
	"context"
	"fmt"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_datamanagement"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
)

func GetFromDataServer(ctx context.Context, traceInfo *traceinfo.TraceInfo, keyList []string, bid uint64, featureIds []uint64) map[string]map[uint64]*o2oalgo_datamanagement.Data {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingDataServer, time.Since(pt))
	}()
	dataMap := make(map[string]map[uint64]*o2oalgo_datamanagement.Data, len(keyList)) // key -> featureId -> Data
	rsp, err := integrate.DataManageServiceClient.GetData(ctx, bid, keyList)
	metric_reporter2.ReportDurationAndQPS(time.Since(pt), metric_reporter2.SearchReportTypeRpc, "", "", fmt.Sprintf("GetData_%d", bid))

	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_META_ERROR)
		logkit.FromContext(ctx).Error("failed to GetData", logkit.Err(err), logkit.Uint64("bid", bid), logkit.Any("featureIds", featureIds), logkit.Any("keyList", keyList))
		return dataMap
	}
	if rsp == nil || rsp.GetItems() == nil || len(rsp.GetItems()) == 0 {
		logkit.FromContext(ctx).Error("GetData is nil", logkit.Err(err), logkit.Uint64("bid", bid), logkit.Any("featureIds", featureIds), logkit.Any("keyList", keyList))
		return dataMap
	}
	itemMap := rsp.GetItems()
	for _, key := range keyList {
		val, ok := itemMap[key]
		if !ok || val == nil {
			logkit.FromContext(ctx).Error("val is nil", logkit.Any("key", key))
			continue
		}
		for _, featureId := range featureIds {
			if val.GetData() == nil {
				continue
			}
			mem, exist := val.GetData()[featureId]
			if !exist || mem == nil {
				logkit.FromContext(ctx).Error("mem is nil", logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId), logkit.String("key", key))
				continue
			}
			if dataMap[key] == nil {
				dataMap[key] = make(map[uint64]*o2oalgo_datamanagement.Data)
			}
			dataMap[key][featureId] = mem
		}

	}
	return dataMap
}

func GetFromDataServerSingleKeySingleFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo, key string, bid uint64, featureId uint64) *o2oalgo_datamanagement.Data {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStorePrice, time.Since(pt))
	}()
	rsp, err := integrate.DataManageServiceClient.GetData(ctx, bid, []string{key})
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_META_ERROR)
		logkit.FromContext(ctx).Error("failed to GetData", logkit.Err(err), logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId), logkit.String("key", key))
		return nil
	}
	if rsp == nil || rsp.GetItems() == nil || len(rsp.GetItems()) == 0 {
		logkit.FromContext(ctx).Error("GetData is nil", logkit.Err(err), logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId), logkit.String("key", key))
		return nil
	}
	itemMap := rsp.GetItems()
	val, ok := itemMap[key]
	if !ok || val == nil || val.GetData() == nil {
		logkit.FromContext(ctx).Error("val is nil", logkit.Any("key", key))
		return nil
	}
	data, exist := val.GetData()[featureId]
	if !exist || data == nil {
		logkit.FromContext(ctx).Error("mem is nil", logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId), logkit.String("key", key))
		return nil
	}
	return data
}
