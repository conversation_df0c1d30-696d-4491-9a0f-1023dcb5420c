package rediscache

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/geohash"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/gogo/protobuf/proto"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	Expiration                = 5 * time.Minute
	GeoHashLen                = 7
	SearchCacheKey            = "foodalgo:search:mix:v1:%v:%v:%d:%d:%d:%d:%d:%d:%s:%s"
	SearchCacheKeyForVN       = "foodalgo:search:mix2:v1:%s:%d:%s:%s"
	SearchCacheKey2           = "foodalgo:search:mix2:v1:%s:%d:%d:%s:%s"            // publish id key
	MixResultCacheKey         = "foodalgo:search:mix:result:%s:%s:%s:%d:%d:%s:%s"   // publishId, keyword, geo, handlerType, pipelineType, env, cid
	LiseWiseResultCacheKey    = "foodalgo:search:listwise:result:%s:%s:%s:%d:%s:%s" // publishId, keyword, geo, handlerType, env, cid
	PrepaidInfoCacheKey       = "foodalgo:search:prepaid:%s:%s:%d:%s:%s"            // publishId, keyword, handlerType, env, cid
	promotionShopFlag         = 1
	promotionFreeFreeShipping = 2
	promotionDishFlag         = 4
	NotRating                 = 0
	Rating50                  = 1
	Rating45                  = 2
	Rating40                  = 3
	SortRelevance             = 1
	SortNearby                = 2
	SortTopSales              = 3
	SortBestRated             = 4
	HalalType                 = 1
	FilterByPreferred         = 1
)

type MixSearchResultCache struct {
	client *redis.Client
}

func NewMixSearchResultCache(client *redis.Client) *MixSearchResultCache {
	return &MixSearchResultCache{
		client: client,
	}
}

func GetRatingFilterType(filter *foodalgo_search.SearchRequest_FilterType) int32 {
	var ret int32
	ret = 0
	if filter != nil {
		switch filter.GetRatingFilter() {
		case foodalgo_search.SearchRequest_NotRating:
			ret = NotRating
		case foodalgo_search.SearchRequest_Rating_50:
			ret = Rating50
		case foodalgo_search.SearchRequest_Rating_45:
			ret = Rating45
		case foodalgo_search.SearchRequest_Rating_40:
			ret = Rating40
		default:
			ret = NotRating
		}
	}
	return ret
}

func GetPromotionType(filter *foodalgo_search.SearchRequest_FilterType) int32 {
	var ret int32
	ret = 0
	if filter != nil && filter.GetPromotionFilter() != nil {
		for _, filterType := range filter.GetPromotionFilter() {
			switch filterType {
			case foodalgo_search.SearchRequest_ShopDiscount:
				ret = ret | promotionShopFlag
			case foodalgo_search.SearchRequest_FreeShipping:
				ret = ret | promotionFreeFreeShipping
			case foodalgo_search.SearchRequest_DishPromotion:
				ret = ret | promotionDishFlag
			}
		}
	}
	return ret
}

func GetSortType(sortType foodalgo_search.SearchRequest_SortType) int32 {
	var ret int32
	ret = 1
	switch sortType {
	case foodalgo_search.SearchRequest_Relevance:
		ret = SortRelevance
	case foodalgo_search.SearchRequest_Nearby:
		ret = SortNearby
	case foodalgo_search.SearchRequest_TopSales:
		ret = SortTopSales
	case foodalgo_search.SearchRequest_BestRated:
		ret = SortBestRated
	}
	return ret
}

func GetShopType(filter *foodalgo_search.SearchRequest_FilterType) int32 {
	var ret int32
	ret = 0
	if env.GetCID() == cid.MY {
		if filter != nil {
			switch filter.GetShopFilter() {
			case foodalgo_search.SearchRequest_Halal:
				ret = HalalType
			}
		}
	}
	return ret
}

func GetPreferredFilter(filter *foodalgo_search.SearchRequest_FilterType) int32 {
	var ret int32
	ret = 0
	if filter != nil {
		if filter.GetIsPreferredMerchant() {
			ret = FilterByPreferred
		}
	}
	return ret
}

func getMixerResultCacheKey(ctx context.Context, traceInfo *traceinfo.TraceInfo) string {
	publishIdKey := traceInfo.TraceRequest.PublishId
	geo := geohash.RedisEncodeWithPrecision(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude, GeoHashLen)
	if len(publishIdKey) == 0 {
		// publish 为空时，需要单独用请求字段作为缓存key
		logger.MyDebug(ctx, traceInfo.IsDebug, "getMixerResultCacheKey publishId is nil", logkit.Any("req", traceInfo.TraceRequest))
		promotionType := GetPromotionType(traceInfo.TraceRequest.FilterType)
		ratingFilterType := GetRatingFilterType(traceInfo.TraceRequest.FilterType)
		sortType := GetSortType(traceInfo.TraceRequest.GetSortType())
		shopType := GetShopType(traceInfo.TraceRequest.FilterType)
		preferredFilter := GetPreferredFilter(traceInfo.TraceRequest.FilterType)
		publishIdKey = fmt.Sprintf("%s:%s:%d:%d:%d:%d:%d", traceInfo.QueryKeyword, geo, promotionType, ratingFilterType, sortType, shopType, preferredFilter)
	}
	key := fmt.Sprintf(MixResultCacheKey, publishIdKey, strings.ToLower(traceInfo.TraceRequest.QueryRaw), geo, traceInfo.HandlerType, traceInfo.PipelineType, env.GetEnv(), env.GetCID())
	logger.MyDebug(ctx, traceInfo.IsDebug, "getMixerResultCacheKey search result cache key", zap.String("key", key))
	return key
}

func (r *MixSearchResultCache) GetMixerResult(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*foodalgo_search.SearchResponse_IDPair, error) {
	hit := 0
	data := foodalgo_search.SearchResponse{}
	key := getMixerResultCacheKey(ctx, traceInfo)
	defer func() {
		logkit.FromContext(ctx).Info("SearchAndMixResult id pairs from cache", logkit.String("key", key), logkit.Int("size", len(data.GetIds())))
		metric_reporter2.ReportHitRateRequest(1, hit, "GetMixerResult")
	}()
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetMixerCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "GetMixerCache")
	if err != nil && err != redis.Nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetMixerResult get redis failed",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetMixerCache", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "GetMixerCache", "0")
	if err == redis.Nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "GetMixerResult nil from cache", logkit.String("key", key))
		return nil, nil
	}
	err = proto.Unmarshal([]byte(val), &data)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"SearchGlobal MixSearchResultCache.GetMixerResult redis get",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetMixerCache.Unmarshal", "failed")
		return nil, fmt.Errorf("parse to gogo proto error")
	}
	reporter.ReportClientRequestError(1, "GetMixerCache.Unmarshal", "0")
	if traceInfo.IsDebug {
		cacheInfoStr, _ := json.Marshal(data)
		logkit.FromContext(ctx).Info("GetMixerResult", zap.String("key", key), zap.String("cache info", string(cacheInfoStr)))
	}
	hit = 1
	return data.GetIds(), nil
}

func (r *MixSearchResultCache) SetMixerResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, data []*foodalgo_search.SearchResponse_IDPair) error {
	if len(data) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SetMixerResult data is empty", logkit.Any("req", traceInfo.TraceRequest))
		return nil
	}
	key := getMixerResultCacheKey(ctx, traceInfo)
	rsp := foodalgo_search.SearchResponse{Ids: data}
	val, err := proto.Marshal(&rsp)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SetMixerResult proto marshal failed",
			zap.String("key", key),
			zap.Any("cal", data))
		reporter.ReportClientRequestError(1, "SetMixerCache.Marshal", "failed")
		return fmt.Errorf("parse from proto error")
	}
	reporter.ReportClientRequestError(1, "SetMixerCache.Marshal", "0")
	expireTime := apollo.SearchApolloCfg.UserCacheExpireSeconds
	if expireTime == 0 {
		expireTime = 300 // 300s,5min
	}
	var expire = time.Duration(expireTime) * time.Second
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, expire).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "SetMixerCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "SetMixerCache")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"SearchGlobal search result failed",
			zap.String("key", key),
			zap.String("val", string(val)),
			zap.Any("expire", expire))
		reporter.ReportClientRequestError(1, "SetMixerCache", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "SetMixerCache", "0")
	logkit.FromContext(ctx).Info("SearchAndMixResult id pairs set cache", logkit.String("key", key), logkit.Int("size", len(data)))
	return nil
}

func getLiseWiseCacheInfoCacheKey(ctx context.Context, traceInfo *traceinfo.TraceInfo) string {
	publishIdKey := traceInfo.TraceRequest.PublishId
	geo := geohash.RedisEncodeWithPrecision(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude, GeoHashLen)
	key := fmt.Sprintf(LiseWiseResultCacheKey, publishIdKey, strings.ToLower(traceInfo.TraceRequest.QueryRaw), geo, traceInfo.HandlerType, env.GetEnv(), env.GetCID())
	logger.MyDebug(ctx, traceInfo.IsDebug, "getSearchStoreInfosCacheKey search result cache key", zap.String("key", key))
	return key
}

func (r *MixSearchResultCache) GetLiseWiseCacheInfoCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) (*foodalgo_search.SearchResultCacheInfo, error) {
	hit := 0
	cacheInfo := &foodalgo_search.SearchResultCacheInfo{}
	key := getLiseWiseCacheInfoCacheKey(ctx, traceInfo)
	defer func() {
		logkit.FromContext(ctx).Info("GetLiseWiseCacheInfoCache stores from cache", logkit.String("key", key), logkit.Int("size", len(cacheInfo.GetStores())), logkit.Int("hit", hit))
		metric_reporter2.ReportHitRateRequest(1, hit, "GetLiseWiseCacheInfoCache")
	}()
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "GetLiseWiseCacheInfoCache")
	if err != nil && err != redis.Nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		reporter.ReportClientRequestError(1, "GetLiseWiseCacheInfoCache", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "GetLiseWiseCacheInfoCache", "0")
	if err == redis.Nil {
		return nil, nil
	}
	err = proto.Unmarshal([]byte(val), cacheInfo)
	if err != nil {
		return nil, fmt.Errorf("GetLiseWiseCacheInfoCache parse to gogo proto error")
	}
	if traceInfo.IsDebug {
		cacheInfoStr, _ := json.Marshal(cacheInfo)
		logkit.FromContext(ctx).Info("GetLiseWiseCacheInfoCache", zap.String("key", key), zap.String("cache info", string(cacheInfoStr)))
	}
	hit = 1
	return cacheInfo, nil
}

func (r *MixSearchResultCache) SetLiseWiseCacheInfoCache(ctx context.Context, traceInfo *traceinfo.TraceInfo, cacheInfo *foodalgo_search.SearchResultCacheInfo) error {
	if cacheInfo == nil || len(cacheInfo.GetStores()) == 0 {
		return nil
	}
	key := getLiseWiseCacheInfoCacheKey(ctx, traceInfo)
	if traceInfo.IsDebug {
		cacheInfoStr, _ := json.Marshal(cacheInfo)
		logkit.FromContext(ctx).Info("GetLiseWiseCacheInfoCache", zap.String("key", key), zap.String("cache info", string(cacheInfoStr)))
	}
	val, err := proto.Marshal(cacheInfo)
	if err != nil {
		return fmt.Errorf("parse from proto error")
	}
	expireTime := apollo.SearchApolloCfg.ListWiseCacheExpireSeconds
	if expireTime == 0 {
		expireTime = 900 // 900s,15min
	}
	var expire = time.Duration(expireTime) * time.Second
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, expire).Err()
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "SetLiseWiseCacheInfoCache")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		reporter.ReportClientRequestError(1, "SetLiseWiseCacheInfoCache", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "SetLiseWiseCacheInfoCache", "0")
	logkit.FromContext(ctx).Info("SetLiseWiseCacheInfoCache set cache", logkit.String("key", key), logkit.Int("size", len(cacheInfo.GetStores())))
	return nil
}

func getPrepaidInfoCacheKey(ctx context.Context, traceInfo *traceinfo.TraceInfo) string {
	publishIdKey := traceInfo.TraceRequest.PublishId
	key := fmt.Sprintf(PrepaidInfoCacheKey, publishIdKey, strings.ToLower(traceInfo.TraceRequest.QueryRaw), traceInfo.HandlerType, env.GetEnv(), env.GetCID())
	logger.MyDebug(ctx, traceInfo.IsDebug, "getPrepaidInfoCacheKey search result cache key", zap.String("key", key))
	return key
}

func (r *MixSearchResultCache) GetPrepaidInfoCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) (*foodalgo_search.SearchResultCacheInfo, error) {
	hit := 0
	cacheInfo := &foodalgo_search.SearchResultCacheInfo{}
	key := getPrepaidInfoCacheKey(ctx, traceInfo)
	defer func() {
		logkit.FromContext(ctx).Info("GetPrepaidInfoCache from cache", logkit.String("key", key), logkit.Int("size", len(cacheInfo.GetPrepaids())))
		metric_reporter2.ReportHitRateRequest(1, hit, "GetPrepaidInfoCache")
	}()
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "GetPrepaidInfoCache")
	if err != nil && err != redis.Nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		reporter.ReportClientRequestError(1, "GetPrepaidInfoCache", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "GetPrepaidInfoCache", "0")
	if err == redis.Nil {
		return nil, nil
	}
	err = proto.Unmarshal([]byte(val), cacheInfo)
	if err != nil {
		return nil, fmt.Errorf("GetPrepaidInfoCache parse to gogo proto error")
	}
	if traceInfo.IsDebug {
		cacheInfoStr, _ := json.Marshal(cacheInfo)
		logkit.FromContext(ctx).Info("GetPrepaidInfoCache", zap.String("key", key), zap.String("cache info", string(cacheInfoStr)))
	}
	hit = 1
	return cacheInfo, nil
}

func (r *MixSearchResultCache) SetPrepaidInfoCache(ctx context.Context, traceInfo *traceinfo.TraceInfo, cacheInfo *foodalgo_search.SearchResultCacheInfo) error {
	if cacheInfo == nil || len(cacheInfo.GetPrepaids()) == 0 {
		return nil
	}
	key := getPrepaidInfoCacheKey(ctx, traceInfo)
	if traceInfo.IsDebug {
		cacheInfoStr, _ := json.Marshal(cacheInfo)
		logkit.FromContext(ctx).Info("SetPrepaidInfoCache", zap.String("key", key), zap.String("cache info", string(cacheInfoStr)))
	}
	val, err := proto.Marshal(cacheInfo)
	if err != nil {
		return fmt.Errorf("parse from proto error")
	}
	expireTime := apollo.SearchApolloCfg.ListWiseCacheExpireSeconds
	if expireTime == 0 {
		expireTime = 900 // 900s,15min
	}
	var expire = time.Duration(expireTime) * time.Second
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, expire).Err()
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "SetPrepaidInfoCache")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		reporter.ReportClientRequestError(1, "SetPrepaidInfoCache", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "SetPrepaidInfoCache", "0")
	if traceInfo.IsDebug {
		logkit.FromContext(ctx).Info("SetPrepaidInfoCache set cache", logkit.String("key", key), logkit.Int("size", len(cacheInfo.GetPrepaids())))
	}
	return nil
}
