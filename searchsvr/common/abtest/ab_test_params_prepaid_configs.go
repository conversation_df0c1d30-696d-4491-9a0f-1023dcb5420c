package abtest

import (
	"context"
	"fmt"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
)

func GetDataServerFeatureConfigs(ctx context.Context, abParamClient *SearchParamMultiClient) map[uint64]map[uint64]*config.DataServerFeatureConfig {
	factorConfigNames := abParamClient.GetParamWithString("SearchPrepaid.PrepaidData.FactorList", "PrepaidCtr,PrepaidCvr")
	factorConfigNameList := strings.Split(factorConfigNames, ",")
	if len(factorConfigNameList) == 0 {
		logkit.FromContext(ctx).Error("factorConfigNames is empty, skip")
		return nil
	}
	featureConfigsMap := make(map[uint64]map[uint64]*config.DataServerFeatureConfig, len(factorConfigNameList)) // bid -> featureId -> DataServerFeatureConfig
	for _, confName := range factorConfigNameList {
		confName = strings.TrimSpace(confName)
		if len(confName) == 0 {
			logkit.FromContext(ctx).Error("factorConfigNames has empty string, skip", logkit.String("confName", confName))
			return nil
		}
		prefix := "SearchPrepaid.PrepaidData." + confName + "."
		factorName := abParamClient.GetParamWithString(prefix+"FactorName", "")
		bid := uint64(abParamClient.GetParamWithInt(prefix+"Bid", 0))
		featureId := uint64(abParamClient.GetParamWithInt(prefix+"FeatureId", 0))
		keyType := abParamClient.GetParamWithString(prefix+"KeyType", constants.KeyTypePrepaidId)
		dataKind := abParamClient.GetParamWithString(prefix+"DataKind", constants.DataKindFloat)
		defaultValue := abParamClient.GetParamWithString(prefix+"DefaultValue", "")
		if factorName == "" || bid <= 0 || featureId <= 0 || keyType == "" || dataKind == "" {
			logkit.FromContext(ctx).Error("invalid factor config",
				logkit.String("confName", confName),
				logkit.String("factorName", factorName),
				logkit.Uint64("bid", bid),
				logkit.Uint64("featureId", featureId),
				logkit.String("keyType", keyType),
				logkit.String("dataKind", dataKind))
			continue
		}
		if len(featureConfigsMap[bid]) == 0 {
			featureConfigsMap[bid] = make(map[uint64]*config.DataServerFeatureConfig)
		}
		featureConfigsMap[bid][featureId] = &config.DataServerFeatureConfig{
			FactorName:   factorName,
			Bid:          bid,
			FeatureId:    featureId,
			KeyType:      keyType,
			DataKind:     dataKind,
			DefaultValue: defaultValue,
		}
	}
	return featureConfigsMap
}

func GetPrepaidFusionRankConfig(ctx context.Context, abParamClient *SearchParamMultiClient) *config.FusionRankConfig {
	fusionConfig := &config.FusionRankConfig{
		ExpString:     abParamClient.GetParamWithString("SearchPrepaid.FusionRank.ExpString", "w1*prepaid_sku_ctr+w2*prepaid_sku_cvr"),
		ExpParameters: abParamClient.GetParamWithString("SearchPrepaid.FusionRank.ExpParameters", "w1=1.0,w2=2.0"),
	}
	return fusionConfig
}

func GetPrepaidFewFewResultRecallSizeConfig(ctx context.Context, abParamClient *SearchParamMultiClient, recallName string) int {
	var recallSize int
	switch recallName {
	case constants.SQSUserPrepaidOrderRedirection:
		recallSize = abParamClient.GetParamWithInt("SearchPrepaid.FewResultRecall.UserPrepaidOrderRedirection.RecallSize", 250)
	case constants.SQSUserDishOrderRedirection:
		recallSize = abParamClient.GetParamWithInt("SearchPrepaid.FewResultRecall.UserDishOrderRedirection.RecallSize", 250)
	case constants.SQSHotRecall:
		recallSize = abParamClient.GetParamWithInt("SearchPrepaid.FewResultRecall.HotRecall.RecallSize", 400)
	case constants.SQSHighDiscountRecall:
		recallSize = abParamClient.GetParamWithInt("SearchPrepaid.FewResultRecall.HighDiscountRecall.RecallSize", 400)
	case constants.SQSFewResultMaxSize:
		recallSize = abParamClient.GetParamWithInt("SearchPrepaid.FewResultRecall.RecallMaxSize", 400)
	default:
		logkit.FromContext(ctx).Error("GetPrepaidFewFewResultRecallSizeConfig: unknown recallName", logkit.String("recallName", recallName))
	}
	return recallSize
}

// 融合保量
var quotaMap = map[int]int{
	1: 200,
	2: 150,
	3: 75,
	4: 75,
}

// 融合保量配置
func GetPrepaidFewFewResultPriorityGuaranteedSizeConfig(ctx context.Context, abParamClient *SearchParamMultiClient, priority int) int {
	defaultSize := quotaMap[priority]
	if defaultSize <= 0 {
		defaultSize = 100 // 默认值
	}
	GuaranteedSize := abParamClient.GetParamWithInt(fmt.Sprintf("SearchPrepaid.FewResultRecall.PriorityGuaranteedSize.%d", priority), defaultSize)
	return GuaranteedSize
}

func GetPrepaidFewFewResultPriorityGuaranteedMaxSizeConfig(ctx context.Context, abParamClient *SearchParamMultiClient) int {
	GuaranteedSize := abParamClient.GetParamWithInt("SearchPrepaid.FewResultRecall.PriorityGuaranteedSize.MaxSize", 500)
	return GuaranteedSize
}

func GetPrepaidQueryIntentionBoostConfig(ctx context.Context, abParamClient *SearchParamMultiClient, queryStoreIntention, queryDishIntention string) (float64, float64, float64, float64) {
	var prepaidNameBoot, storeNameBoot, dishNameBoot, brandNameBoot float64
	if len(queryStoreIntention) > 0 {
		dishNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.StoreIntention.DishNameBoot", 0.1)
		storeNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.StoreIntention.StoreNameBoot", 0.45)
		prepaidNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.StoreIntention.PrepaidSkuNameBoot", 0.1)
		brandNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.StoreIntention.BrandNameBoot", 0.35)
	} else if len(queryDishIntention) > 0 {
		dishNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.DishIntention.DishNameBoot", 0.35)
		storeNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.DishIntention.StoreNameBoot", 0.1)
		prepaidNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.DishIntention.PrepaidSkuNameBoot", 0.45)
		brandNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.DishIntention.BrandNameBoot", 0.1)

	} else {
		dishNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.UnknownIntention.DishNameBoot", 0.25)
		storeNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.UnknownIntention.StoreNameBoot", 0.25)
		prepaidNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.UnknownIntention.PrepaidSkuNameBoot", 0.25)
		brandNameBoot = abParamClient.GetParamWithFloat("SearchPrepaid.Recall.UnknownIntention.BrandNameBoot", 0.25)
	}
	return prepaidNameBoot, storeNameBoot, dishNameBoot, brandNameBoot
}

func GetPrepaidDistanceScoreDistanceBucket(ctx context.Context, abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("SearchPrepaid.FusionRank.DistanceScore.DistanceBucket", 1000.0)
}

func GetPrepaidDistanceScoreMaxDistance(ctx context.Context, abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("SearchPrepaid.FusionRank.DistanceScore.MaxDistance", 6.0)
}

func GetPrepaidNormalNumForFewResult(ctx context.Context, abParamClient *SearchParamMultiClient) int {
	return abParamClient.GetParamWithInt("SearchPrepaid.General.NormalNumForFewResult", 5)
}

func GetPrepaidDishesAggRecallSize(ctx context.Context, abParamClient *SearchParamMultiClient) int {
	return abParamClient.GetParamWithInt("SearchPrepaid.General.PrepaidDishesAggRecallSize", 5)
}

func GetPrepaidDishesRecallSize(ctx context.Context, abParamClient *SearchParamMultiClient) int {
	return abParamClient.GetParamWithInt("SearchPrepaid.General.PrepaidDishesRecallSize", 500)
}

func GetSkipPrepaidBrandShuffle(ctx context.Context, abParamClient *SearchParamMultiClient) bool {
	skip := abParamClient.GetParamWithInt("SearchPrepaid.General.RerankSkipBrandShuffle", 0)
	if skip == 0 {
		return false
	}
	return true
}

// 支持一次模型调用一个模型返回多个因子， 暂时不支持一次模型调用多个模型返回多个因子
func GetPrepaidFusionModelConfigs(ctx context.Context, abParamClient *SearchParamMultiClient, isDebug bool) []*config.ModelFactorConfig {
	modelConfigs := make([]*config.ModelFactorConfig, 0, 2)
	factorNames := abParamClient.GetParamWithString("SearchPrepaid.FusionRank.ModelFactorNames", "CTR,REL")
	factorNameList := strings.Split(factorNames, ",")
	if len(factorNameList) == 0 {
		return modelConfigs
	}
	ackFactorName := abParamClient.GetParamWithString("SearchPrepaid.FusionRank.ACKFactorName", "CTR")
	var ackModelName string
	modelConfigMap := make(map[string][]*config.FactorKeyConfig, len(factorNameList))
	for i, factorName := range factorNameList {
		factorName = strings.TrimSpace(factorName)
		if len(factorName) == 0 {
			logkit.FromContext(ctx).Error("factorName has empty string, skip", logkit.Any("factorNameList", factorNameList), logkit.Int("index", i))
			continue
		}
		prefix := "SearchPrepaid.FusionRank.ModelFactor." + factorName + "."
		factorKeyConfig := &config.FactorKeyConfig{
			FactorName:     factorName,
			ModelName:      abParamClient.GetParamWithString(prefix+"ModelName", ""),
			ScoreKey:       abParamClient.GetParamWithString(prefix+"ScoreKey", ""),
			FactorKey:      abParamClient.GetParamWithString(prefix+"FactorKey", ""),
			DefaultScore:   abParamClient.GetParamWithFloat(prefix+"DefaultScore", 0.0),
			MaxScore:       abParamClient.GetParamWithFloat(prefix+"MaxScore", 0.0),
			MinScore:       abParamClient.GetParamWithFloat(prefix+"MinScore", 0.0),
			ItemFeasType:   abParamClient.GetParamWithString(prefix+"ItemFeasType", config.ItemFeasTypePrepaidFeature),
			IsRoughPredict: abParamClient.GetParamWithInt(prefix+"IsRoughPredict", 0),
		}
		if len(factorKeyConfig.ModelName) == 0 || len(factorKeyConfig.ScoreKey) == 0 || len(factorKeyConfig.FactorKey) == 0 {
			logkit.FromContext(ctx).Error("factorKeyConfig has empty string, skip", logkit.Any("factorKeyConfig", factorKeyConfig))
			continue
		}
		if ackFactorName == factorKeyConfig.FactorName {
			ackModelName = factorKeyConfig.ModelName
		}
		if modelConfigMap[factorKeyConfig.ModelName] == nil {
			modelConfigMap[factorKeyConfig.ModelName] = make([]*config.FactorKeyConfig, 0, 2)
		}
		modelConfigMap[factorKeyConfig.ModelName] = append(modelConfigMap[factorKeyConfig.ModelName], factorKeyConfig)
	}
	for modelName, factorKeyConfigs := range modelConfigMap {
		var isRoughPredict bool
		for _, f := range factorKeyConfigs {
			if f.IsRoughPredict == 1 {
				isRoughPredict = true
				break
			}
		}
		modelConfigs = append(modelConfigs, &config.ModelFactorConfig{
			ModelName:        modelName,
			FactorKeyConfigs: factorKeyConfigs,
			IsACKInstance:    modelName == ackModelName,
			IsRoughPredict:   isRoughPredict,
		})
	}
	if isDebug {
		logkit.FromContext(ctx).Info("GetPrepaidFusionModelConfigs", logkit.Any("modelConfigs", modelConfigs))
	}
	return modelConfigs
}
