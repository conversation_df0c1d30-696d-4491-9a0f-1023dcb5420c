package apollo

import (
	"context"
	"encoding/json"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	apollo "git.garena.com/shopee/marketing/config-client"
	configCenter "git.garena.com/shopee/o2o-intelligence/common/common-lib/config_center"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/env"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"go.uber.org/zap"
)

type SearchApolloConfig struct {
	LogLevel                           string               `xml:"LogLevel"`
	MaxQueryLen                        int                  `xml:"MaxQueryLen"`
	AdsRecallDishRecallStoreLength     int                  `xml:"AdsRecallDishRecallStoreLength"`
	ESSearchTrafficSplit               ESSearchTrafficSplit `xml:"ESSearchTrafficSplit"`
	ClientTimeOutConfig                *ClientTimeOutConfig `xml:"ClientTimeOutConfig"`
	DishMetaSkip                       bool                 `xml:"DishMetaSkip"`
	PredictAckNum                      int                  `xml:"PredictAckNum"`
	NewDishRecallBatch                 int                  `xml:"NewDishRecallBatch"`
	NewDishRecallBatchForFS            int                  `xml:"NewDishRecallBatchForFS"`
	NewDishESRecallShardCnt            int                  `xml:"NewDishESRecallShardCnt"`
	NewDishESRecallShardCntMappingStr  string               `xml:"NewDishESRecallShardCntMappingStr"`
	DishRecallAffiliateBatch           int                  `xml:"DishRecallAffiliateBatch"`
	BrandRecallAffiliateBatch          int                  `xml:"BrandRecallAffiliateBatch"`
	StoreMetaBatchSize                 int                  `xml:"StoreMetaBatchSize"`
	PolygonMGetBatchSize               int                  `xml:"PolygonMGetBatchSize"`
	PolygonDistanceFilter              bool                 `xml:"PolygonDistanceFilter"`              // 多边形过滤开关
	PolygonDistanceLocalCacheBatchSize int32                `xml:"PolygonDistanceLocalCacheBatchSize"` // 多边形过滤本地缓存每批size
	AnnRecallExp                       string               `xml:"AnnRecallExp"`                       // base向量召回
	AnnRecallExpV2                     string               `xml:"AnnRecallExpV2"`                     // exp 向量召回
	AnnRecallExpV3                     string               `xml:"AnnRecallExpV3"`                     // exp v3向量召回
	FilterWithStoreDishAvailable       bool                 `xml:"FilterWithStoreDishAvailable"`       // 门店下可售菜品过滤
	PrintESQueryDSL                    bool                 `xml:"PrintESQueryDSL"`                    // 打印ES DSL 语句开关
	StoreCateExtendRecallSize          uint64               `xml:"StoreCateExtendRecallSize"`          // 新分类的扩召回size限制
	StoreCateExp                       string               `xml:"StoreCateExp"`                       // 新分类的实验组
	DishSearcherExp                    string               `xml:"DishSearcherExp"`                    // DishSearcher实验组
	GoGcValue                          int                  `xml:"GoGcValue"`                          // GOGC参数
	QPABTestList                       string               `xml:"QPABTestList"`
	QPNormalCacheSwitch                bool                 `xml:"QPNormalCacheSwitch"`
	FineTuneSwitch                     bool                 `xml:"FineTuneSwitch"`                 // VN 微调因子排序开关
	NonFoodSortRate                    float64              `xml:"NonFoodSortRate"`                // VN non-food 类别排序增强因子
	VNGrayScaleWhiteList               string               `xml:"VNGrayScaleWhiteList"`           // VN 灰度白名单
	VNGrayScaleSegmentMin              int                  `xml:"VNGrayScaleSegmentMin"`          // VN 灰度用户ID段 min
	VNGrayScaleSegmentMax              int                  `xml:"VNGrayScaleSegmentMax"`          // VN 灰度用户ID段 max
	TotalNumGEORoutingMatrixSwitch     bool                 `xml:"TotalNumGEORoutingMatrixSwitch"` // totalnum 调用map matrix api 开关
	GEORoutingMatrixSwitch             bool                 `xml:"GEORoutingMatrixSwitch"`         // 调用map matrix api 开关
	GEORoutingMatrixBatchSize          int                  `xml:"GEORoutingMatrixBatchSize"`      // 调用map matrix api 批量控制
	GEORoutingMatrixDistanceLimit      int                  `xml:"GEORoutingMatrixDistanceLimit"`  // 调用map matrix api 距离限制，0 不限制
	GEORoutingMatrixSearchType         int                  `xml:"GEORoutingMatrixSearchType"`     // 调用map matrix api 类型。0-全部门店请求，1-仅请求brand 相关
	ABTestBlockList                    string               `xml:"ABTestBlockList"`                // ab test 上报实验列表，精简x-ab-test 字段
	ABTestBlockMap                     *ABTestBlockControl  // 将ABTestBlockList转化为map
	NightTimeStart                     string               `xml:"NightTimeStart"`                     // 夜间沉底：start
	NightTimeEnd                       string               `xml:"NightTimeEnd"`                       // 夜间沉底: end
	VNHttpClientTimeoutMillisecond     int                  `xml:"VNHttpClientTimeoutMillisecond"`     // 300 ms
	VNHttpClientIdleConnTimeoutSeconds int                  `xml:"VNHttpClientIdleConnTimeoutSeconds"` // 空闲回收等待的秒数
	VNHttpClientMaxIdleConnsPerHost    int                  `xml:"VNHttpClientMaxIdleConnsPerHost"`    // vn http client 每个 host 的最大的等待连接数
	VNHttpClientMaxIdleConns           int                  `xml:"VNHttpClientMaxIdleConns"`           // vn http client 最大的等待连接数
	VNRecallSize                       int                  `xml:"VNRecallSize"`                       // vn 召回 size
	VNIsFilterOpening                  bool                 `xml:"VNIsFilterOpening"`                  // vn 是否打开 filter isopening 的开关
	VNIsFilterPromotion                bool                 `xml:"VNIsFilterPromotion"`                // vn 是否打开 filter promotion 的开关
	UserCacheExpireSeconds             int                  `xml:"UserCacheExpireSeconds"`             // 用户结果缓存seconds
	ListWiseCacheExpireSeconds         int                  `xml:"ListWiseCacheExpireSeconds"`         // listwise用户结果缓存seconds
	AffiliateCacheExpireSeconds        int                  `xml:"AffiliateCacheExpireSeconds"`        // 佣金结果缓存seconds
	CloseOptRotateMerchants            bool                 `xml:"CloseOptRotateMerchants"`            // 关闭干预随机调整门店的功能
	AdsInterventionOptSwitch           bool                 `xml:"AdsInterventionOptSwitch"`           // 0 不开启，100 全部走新逻辑
	VNCloseNewCorrected                bool                 `xml:"VNCloseNewCorrected"`                // VN 关闭新纠错开关
	VNDishWithRewriteGroups            string               `xml:"VNDishWithRewriteGroups"`            // VN挂菜带 rewrite 词
	VNOsrmDistanceSwitch               int                  `xml:"VNOsrmDistanceSwitch"`               // 0 不开启，1 开启ab实验， 100 全部走新逻辑
	VNOsrmDistanceExps                 string               `xml:"VNOsrmDistanceExps"`                 // VN 是否使用 OSRM 的实验
	VNOsrmDistanceBikeUrl              string               `xml:"VNOsrmDistanceBikeUrl"`              // VN OSRM Bike Url
	VNOsrmDistanceCarUrl               string               `xml:"VNOsrmDistanceCarUrl"`               // VN OSRM Car Url
	VNOsrmDistanceTimeoutMs            int                  `xml:"VNOsrmDistanceTimeoutMs"`            // VN OSRM Timeout, ms
	FoodyDeliverySwitch                int                  `xml:"FoodyDeliverySwitch"`                // 0 不开启，1 开启ab实验， 100 全部走新逻辑
	FoodyDeliveryExp                   string               `xml:"FoodyDeliveryExp"`
	HideNonHalalStartUnixTimeSec       int64                `xml:"HideNonHalalStartUnixTimeSec"`   // 斋月隐藏non-halal, start
	HideNonHalalEndUnixTimeSec         int64                `xml:"HideNonHalalEndUnixTimeSec"`     // 斋月隐藏non-halal, end
	MultiFactorUseNew                  bool                 `xml:"MultiFactorUseNew"`              // 是否使用多因子新公式
	CloseSearchDishesCache             bool                 `xml:"CloseSearchDishesCache"`         // 关闭门店下搜菜缓存
	CloseSearchDishL3CategoryCache     bool                 `xml:"CloseSearchDishL3CategoryCache"` // 关闭门店下搜菜L3聚合缓存

	// 主站分数
	MainSiteDishTextMatchDefaultScore float64 `xml:"MainSiteDishTextMatchDefaultScore"` // storeSort的时候，DishTextMatchScore 默认 score
	MainSiteStoreScoreExpression      string  `xml:"MainSiteStoreScoreExpression"`      // storeSort的时候，exp
	MainSiteStoreScoreWeightMap       string  `xml:"MainSiteStoreScoreWeightMap"`       // storeSort的时候，权重 map = {"w1": 0.1, "w2": 0.3, "w3": 0.1}
	MainSiteStoreTruncTextMatchScore  float64 `xml:"MainSiteStoreTruncTextMatchScore"`  // storeSort的时候，StoreTruncTextMatch 的 截断分数
	MainSiteDishScoreExpression       string  `xml:"MainSiteDishScoreExpression"`       // dishSort的时候，exp
	MainSiteDishScoreWeightMap        string  `xml:"MainSiteDishScoreWeightMap"`        // dishSort的时候，权重 map = {"w1": 0.1, "w2": 0.3, "w3": 0.1}
	MainSiteDishTruncSalesVolume      float64 `xml:"MainSiteDishTruncSalesVolume"`      // dishSort的时候，SalesVolume 的截断分数
	MainSiteDishTruncTextMatchScore   float64 `xml:"MainSiteDishTruncTextMatchScore"`   // dishSort的时候，DishTextMatchScore 的 截断分数
	MainSiteDumpLogKafkaTopicName     string  `xml:"MainSiteDumpLogKafkaTopicName"`     // 主站 dumplog 发送的 topicName
	MainSiteDumpLogCutSize            int     `xml:"MainSiteDumpLogCutSize"`            // 主站 dumplog 发送的 cutSize，默认 200
	MainSiteVNRoutingDistanceLimit    int     `xml:"MainSiteVNRoutingDistanceLimit"`    // VN 主站routing distance 过滤

	CloseSearchStoresAffiliateCache bool `xml:"CloseSearchStoresAffiliateCache"` // 关闭门店下搜菜缓存
	CloseSearchDishesAffiliateCache bool `xml:"CloseSearchDishesAffiliateCache"` // 关闭门店下搜菜缓存

	// 补充挂菜remove ID 菜品补充召回开关
	//OpenDishSupplementRecall bool `xml:"OpenDishSupplementRecall"`
	//OpenKeywordLabelControl  bool `xml:"OpenKeywordLabelControl"`
	// 降级配置
	DowngradeServerConfig DowngradeServerConfig `xml:"DowngradeServerConfig"`
	RecallDowngradeConfig RecallDowngradeConfig `xml:"RecallDowngradeConfig"`

	LevelDowngradeConfigList LevelDowngradeConfigList `xml:"LevelDowngradeConfigList"`
	LevelDowngradeConfigMap  *LevelDowngradeConfigControl

	PrintRecallStat bool `xml:"PrintRecallStat"` // 打印召回统计日志开关

	// 召回配置化
	RecallConfigurationHitDishRecallSwitch int    `xml:"RecallConfigurationHitDishRecallSwitch"` // 0 不开启，1 开启ab实验， 100 全部走新逻辑
	RecallConfigurationHitDishRecallExps   string `xml:"RecallConfigurationHitDishRecallGroups"` // 召回配置化的上层控制 group

	CtrModelName                     string      `xml:"CtrModelName"`
	CvrModelName                     string      `xml:"CvrModelName"`
	WhiteListSwitch                  int32       `xml:"WhiteListSwitch"`
	WhiteListOfUat                   string      `xml:"WhiteListOfUat"`
	RecallExpVariate                 string      `xml:"RecallExpVariate"`
	RecallExpConf                    string      `xml:"RecallExpConf"`
	RankExpVariate                   string      `xml:"RankExpVariate"`
	DishRecallExpVariate             string      `xml:"DishRecallExpVariate"`
	DishRecallExpCaller              string      `xml:"DishRecallExpCaller"`
	DishRecallCorExp                 string      `xml:"DishRecallCorExp"`
	DishRecallTermLimit              int         `xml:"DishRecallTermLimit"`
	QueryStringNerMap                string      `xml:"QueryStringNerMap"` // ner - query string 映射表
	NewAbConfig                      NewAbConfig `xml:"NewAbConfig"`
	PredictRelTruncate               int         `xml:"PredictRelTruncate"`
	RecallTypeMappings               string      `xml:"RecallTypeMappings"` // recallType 映射表
	DFExpVariate                     string      `xml:"DFExpVariate"`
	NewDFExpVariate                  string      `xml:"NewDFExpVariate"`
	DFCaller                         string      `xml:"DFCaller"`
	QueryNormExp                     string      `xml:"QueryNormExp"`
	FSUserTagName                    string      `xml:"FSUserTagName"`
	ShippingFeeReqBatch              int         `xml:"ShippingFeeReqBatch"`
	StoreTagsReqBatch                int         `xml:"StoreTagsReqBatch"`
	ESCacheTime                      int         `xml:"ESCacheTime"`
	StorePriceBid                    uint64      `xml:"StorePriceBid"`
	PriceFeatureId                   uint64      `xml:"PriceFeatureId"`
	FSDishRecallBatchSize            int32       `xml:"FSDishRecallBatchSize"`
	FSDishFeatureItemBatchSize       int32       `xml:"FSDishFeatureItemBatchSize"`
	FSDishFeatureCacheBatchSize      int32       `xml:"FSDishFeatureCacheBatchSize"`
	FSDishFSRecallBatchSize          int32       `xml:"FSDishFSRecallBatchSize"`
	ESDishRecallFillingTagsBatchSize int32       `xml:"ESDishRecallFillingTagsBatchSize"`
	StoreDishesCacheBatchSize        int32       `xml:"StoreDishesCacheBatchSize"`
	DishFlashSaleCacheBatchSize      int32       `xml:"DishFlashSaleCacheBatchSize"`
	FSPrepaidUserHistoryBatchSize    int32       `xml:"FSPrepaidUserHistoryBatchSize"`
	FSPrepaidYearSalesBatchSize      int32       `xml:"FSPrepaidYearSalesBatchSize"`

	StoreSegmentBid       uint64 `xml:"StoreSegmentBid"`
	StoreSegmentFeatureId uint64 `xml:"StoreSegmentFeatureId"`

	AbtestParamsSwitch         AbtestParamsSwitch `xml:"AbtestParamsSwitch"`
	DishFreeCacheSize          int                `xml:"DishFreeCacheSize"`
	DishFreeCacheTime          int                `xml:"DishFreeCacheTime"`
	StoreDishFreeCacheTime     int                `xml:"StoreDishFreeCacheTime"`
	DishFlashSaleFreeCacheSize int                `xml:"DishFlashSaleFreeCacheSize"`
	MultiFactorLayerId         string             `xml:"MultiFactorLayerId"`

	// 历史订单
	HistoryOrderUseRouting            int    `xml:"HistoryOrderUseRouting"`            // 是否走routing
	HistoryOrderValidStatus           string `xml:"HistoryOrderValidStatus"`           // 合法订单状态："1,2,3"
	HistoryOrderSearchBeforeDays      int    `xml:"HistoryOrderSearchBeforeDays"`      // 合法订单状态从哪天开始，默认一年前：365天
	HistoryOrderSearchMatchPhraseSlop int    `xml:"HistoryOrderSearchMatchPhraseSlop"` // MatchPhrase的间隔词，默认 10

	// 新菜品挂菜&特征
	DishFeatureCacheSize int `xml:"DishFeatureCacheSize"` // 菜品特征缓存大小
	DishListCacheSize    int `xml:"DishListCacheSize"`    // 门店菜品列表缓存大小
	DishFeatureCacheTTL  int `xml:"DishFeatureCacheTTL"`  // 菜品特征缓存TTL（秒）
	DishListCacheTTL     int `xml:"DishListCacheTTL"`     // 门店菜品列表缓存TTL（秒）
	FsCacheTTL           int `xml:"FsCacheTTL"`           // fs缓存TTL（秒），默认300
	FsCacheSizeBytes     int `xml:"FsCacheSizeBytes"`     // fs缓存size, 默认100MB

	// 易混淆的 category ID 列表
	EasyConfuseCategoryIDList string `xml:"EasyConfuseCategoryIDList"`

	DumpLogKafkaTopicName string `xml:"DumpLogKafkaTopicName"` // kafka dump log topic

	// Store Real Name cache Size
	StoreRealNameCacheSize     int `xml:"StoreRealNameCacheSize"` // Store Real Name cache size, 单位 MB
	StoreRankFactorCacheSize   int `xml:"StoreRankFactorCacheSize"`
	StoreOfflineCtCvrCacheSize int `xml:"StoreOfflineCtCvrCacheSize"`
}

// 启动服务的时候需要初始化Apollo配置
func InitSearchEngineApolloConfig(group, project, namespace, secret string, region env.RegionCode) error {
	// 1.配置回调函数，实际更新操作。配置转化操作也是放在这里，初始化&更新通知都是调用回调函数
	registerSearchEngineCallBackMap()

	newClientParam := configCenter.NewClientParam{
		Watcher:     searchEngineApolloChangeWatcher, // 2.监听，有更新事件触发后，会调用回调函数
		CallbackMap: callBackHandlerMap,              // 3.设置回调函数
		Group:       group,
		Project:     project,
		Namespace:   namespace,
		Secret:      secret,
	}
	client, err := configCenter.NewConfigCenterClientWithRegion(newClientParam, region, false)
	if err != nil {
		logkit.Error("apollo.NewClient", logkit.Err(err))
		return err
	}
	if err := client.Start(); err != nil {
		logkit.Error("client.Start", logkit.Err(err))
		return err
	}
	logkit.Info("apollo client start ok")
	initParam := configCenter.InitConfigCenterParam{
		Config:      SearchApolloCfg,
		Client:      client,
		Namespace:   namespace,
		CallbackMap: callBackHandlerMap,
	}
	configCenter.InitFromConfigCenter(initParam) // 开始，初始化一次。实际是调用回调函数
	return nil
}

func registerSearchEngineCallBackMap() {
	callBackHandlerMap = configCenter.ConfigCenterCallbackMap{
		"content": configCenter.CreateCommonCallback(
			func() interface{} { return &SearchApolloConfig{} },
			func(cfg, value interface{}) {
				mutex.Lock()
				defer mutex.Unlock()
				SearchApolloCfg = value.(*SearchApolloConfig)
				updateLogLevel(*SearchApolloCfg) // 各种根据配置转化的在这里实现一次，初始化&更新通知都会触发
				updateABTestBlockMap(SearchApolloCfg)
				updateRankExpConf(SearchApolloCfg)
				updateRecallExpConf(SearchApolloCfg)
				updateDishRecallConf(SearchApolloCfg)
				updateQueryStringNerConfMap(SearchApolloCfg)
				updateRecallTypeMap(SearchApolloCfg)
				updateLevelDowngradeConfig(SearchApolloCfg)
				updateEasyConfuseCategoryMap(SearchApolloCfg)
			},
		)}
}

func searchEngineApolloChangeWatcher(changeEvent *apollo.ChangeEvent) {
	logkit.Info("apollo config change", logkit.Any("namespace", changeEvent.Namespace))
	param := configCenter.UpdateConfigCenterParam{
		ChangeEvent: changeEvent,
		Config:      SearchApolloCfg,
		CallbackMap: callBackHandlerMap,
	}
	configCenter.UpdateFromConfigCenter(param)
}

func updateLogLevel(config SearchApolloConfig) {
	switch config.LogLevel {
	case "fatal", "info", "debug", "warn", "error", "dpanic", "panic":
		err := logkit.SetLevel(config.LogLevel)
		if err != nil {
			logkit.Error("log SetLevel failed", zap.Any("apollo config", config))
		}
	default:
		logkit.Error("invalid log lever", zap.Any("apollo config", config))
	}
	if config.GoGcValue > 0 {
		// 设置GOGC参数
		debug.SetGCPercent(config.GoGcValue)
		logkit.Info("set GOGC", zap.Int("value", config.GoGcValue))
	}
}

func updateABTestBlockMap(config *SearchApolloConfig) {
	if config.ABTestBlockMap == nil {
		config.ABTestBlockMap = &ABTestBlockControl{
			RWMutex: sync.RWMutex{},
		}
	}
	ABTestBlockMapTmp := make(map[string]bool)
	if len(config.ABTestBlockList) > 0 {
		exps := strings.Split(config.ABTestBlockList, ",")
		for _, exp := range exps {
			key := strings.TrimSpace(exp)
			if len(key) == 0 {
				continue
			}
			ABTestBlockMapTmp[key] = true
		}
	}
	config.ABTestBlockMap.Lock()
	defer config.ABTestBlockMap.Unlock()
	config.ABTestBlockMap.ABTestBlockMap = ABTestBlockMapTmp
}

func updateRankExpConf(config *SearchApolloConfig) {
	if len(config.DFCaller) > 0 {
		expStr := strings.Replace(config.DFCaller, "\n", "", -1)
		expStr = strings.Replace(expStr, " ", "", -1)
		groups := strings.Split(expStr, ";")
		dfCallers = make(map[string]string, 0)
		for _, val := range groups {
			pairs := strings.Split(val, ":")
			if len(pairs) == 2 {
				dfCallers[pairs[0]] = pairs[1]
			}
		}
	}
}

func updateRecallExpConf(config *SearchApolloConfig) {
	if len(config.RecallExpConf) > 0 {
		expStr := strings.Replace(config.RecallExpConf, "\n", "", -1)
		expStr = strings.Replace(expStr, " ", "", -1)
		groups := strings.Split(expStr, ";")
		recallExpConfMap = make(map[string]map[string]float64, 0)
		for _, val := range groups {
			groupWeight := strings.Split(val, ":")
			if len(groupWeight) == 2 {
				confMap := make(map[string]float64)
				weights := strings.Split(groupWeight[1], ",")
				for _, mem := range weights {
					pairs := strings.Split(mem, "=")
					if len(pairs) == 2 {
						if weight, err := strconv.ParseFloat(pairs[1], 64); err == nil {
							confMap[pairs[0]] = weight
						}
					}
				}
				recallExpConfMap[groupWeight[0]] = confMap
			}
		}
	}
}

func updateDishRecallConf(config *SearchApolloConfig) {
	if len(config.DishRecallExpCaller) > 0 {
		expStr := strings.Replace(config.DishRecallExpCaller, "\n", "", -1)
		expStr = strings.Replace(expStr, " ", "", -1)
		groups := strings.Split(expStr, ";")
		dishRecallConfMap = make(map[string]map[string]float32, 0)
		for _, val := range groups {
			gConf := strings.Split(val, ":")
			if len(gConf) == 2 {
				confMap := make(map[string]float32)
				confs := strings.Split(gConf[1], ",")
				for _, mem := range confs {
					pairs := strings.Split(mem, "=")
					if len(pairs) == 2 {
						if weight, err := strconv.ParseFloat(pairs[1], 32); err == nil {
							confMap[pairs[0]] = float32(weight)
						}
					}
				}
				dishRecallConfMap[gConf[0]] = confMap
			}
		}
	}
}

func updateLevelDowngradeConfig(config *SearchApolloConfig) {
	if config.LevelDowngradeConfigMap == nil {
		config.LevelDowngradeConfigMap = &LevelDowngradeConfigControl{
			RWMutex: sync.RWMutex{},
		}
	}
	LevelDowngradeMapTmp := make(map[string]*LevelDowngradeConfig)
	for i := range config.LevelDowngradeConfigList.LevelDowngradeConfigs {
		val := config.LevelDowngradeConfigList.LevelDowngradeConfigs[i]
		key := val.Level
		LevelDowngradeMapTmp[key] = &val
	}

	config.LevelDowngradeConfigMap.Lock()
	config.LevelDowngradeConfigMap.LevelDowngradeConfigMap = LevelDowngradeMapTmp
	defer config.LevelDowngradeConfigMap.Unlock()
}

var easyConfuseCategoryIDMap map[uint32]struct{}

func updateEasyConfuseCategoryMap(config *SearchApolloConfig) {
	categoryIDList := strings.Split(config.EasyConfuseCategoryIDList, ",")
	tmpMap := make(map[uint32]struct{})
	for _, val := range categoryIDList {
		if id, err := strconv.Atoi(val); err == nil {
			tmpMap[uint32(id)] = struct{}{}
		}
	}
	easyConfuseCategoryIDMap = tmpMap
}

func GetEasyConfuseCategoryID() map[uint32]struct{} {
	mutex.RLock()
	defer mutex.RUnlock()
	if easyConfuseCategoryIDMap == nil {
		easyConfuseCategoryIDMap = make(map[uint32]struct{})
	}
	return easyConfuseCategoryIDMap
}

type ClientTimeOutConfig struct {
	FeatureClientTimeOut                 int `xml:"FeatureClientTimeOut"`                 //单位 ms
	FeatureClientUserI2IRecallTimeOut    int `xml:"FeatureClientUserI2IRecallTimeOut"`    //单位 ms
	PredictClientTimeOut                 int `xml:"PredictClientTimeOut"`                 //单位 ms
	PredictAckClientTimeOut              int `xml:"PredictAckClientTimeOut"`              //单位 ms
	QPClientTimeOut                      int `xml:"QPClientTimeOut"`                      //单位 ms
	SearchDishTimeOut                    int `xml:"SearchDishTimeOut"`                    //单位 ms
	SearchStoreTimeOut                   int `xml:"SearchStoreTimeOut"`                   //单位 ms
	SearchPrepaidTimeOut                 int `xml:"SearchPrepaidTimeOut"`                 //单位 ms
	SearchHistoryOrderTimeOut            int `xml:"SearchHistoryOrderTimeOut"`            //单位 ms
	SearchStoreIntentionTimeOut          int `xml:"SearchStoreIntentionTimeOut"`          //单位 ms
	SearchStoreWithDishTimeOut           int `xml:"SearchStoreWithDishTimeOut"`           //单位 ms
	SearchStoreCateExpandTimeOut         int `xml:"SearchStoreCateExpandTimeOut"`         //单位 ms
	SearchDishTimeOutMainSite            int `xml:"SearchDishTimeOutMainSite"`            //单位 ms
	SearchStoreTimeOutMainSite           int `xml:"SearchStoreTimeOutMainSite"`           //单位 ms
	SearchIntentionTimeOutMainSite       int `xml:"SearchIntentionTimeOutMainSite"`       //单位 ms
	SearchPromotionTimeOut               int `xml:"SearchPromotionTimeOut"`               //单位 ms
	RelevanceTimeOut                     int `xml:"RelevanceTimeOut"`                     //单位 ms
	RelevancePredictTimeOut              int `xml:"RelevancePredictTimeOut"`              //单位 ms
	DataManageTimeOut                    int `xml:"DataManageTimeOut"`                    //单位 ms
	DataManageClientCacheTime            int `xml:"DataManageClientCacheTime"`            //单位 ms
	SemanticTimeOut                      int `xml:"SemanticTimeOut"`                      //单位 ms
	SearchStoreWithDishTimeOutMainSite   int `xml:"SearchStoreWithDishTimeOutMainSite"`   //单位 ms
	SearchStoreCateExpandTimeOutMainSite int `xml:"SearchStoreCateExpandTimeOutMainSite"` //单位 ms
	DishSearcherTimeOut                  int `xml:"DishSearcherTimeOut"`                  //单位 ms
	SearchStoreInterventionTimeOut       int `xml:"SearchStoreInterventionTimeOut"`       //单位 ms
	SearchStoreUnsettledMainTagTimeOut   int `xml:"SearchStoreUnsettledMainTagTimeOut"`   //单位 ms
	SearchStoreUnsettledTagsTimeOut      int `xml:"SearchStoreUnsettledTagsTimeOut"`      //单位 ms
	SearchStoreUnsettledDishTimeOut      int `xml:"SearchStoreUnsettledDishTimeOut"`      //单位 ms
	AdsSearchTimeOut                     int `xml:"AdsSearchTimeOut"`
	MixerTimeOut                         int `xml:"MixerTimeOut"`
	DefaultRecallTimeout                 int `xml:"DefaultRecallTimeout"`               // 召回如果没配置 timeout，就用该 timeout，单位 ms
	GEORoutingMatrixTimeOut              int `xml:"GEORoutingMatrixTimeOut"`            //单位 ms
	SearchStoreNerRecallTimeOut          int `xml:"SearchStoreNerRecallTimeOut"`        //单位 ms
	SearchRewriteNerRecallTimeOut        int `xml:"SearchRewriteNerRecallTimeOut"`      //单位 ms
	SearchAnnRecallTimeOut               int `xml:"SearchAnnRecallTimeOut"`             //单位 ms
	SearchVectorEngineRecallTimeOut      int `xml:"SearchVectorEngineRecallTimeOut"`    //单位 ms
	SearchTextFieldRecallANDTimeOut      int `xml:"SearchTextFieldRecallANDTimeOut"`    //单位 ms
	SearchTextFieldRecallORTimeOut       int `xml:"SearchTextFieldRecallORTimeOut"`     //单位 ms
	SearchTextFieldRecallNearORTimeOut   int `xml:"SearchTextFieldRecallNearORTimeOut"` //单位 ms
	SearchQueryTagRecallTimeOut          int `xml:"SearchQueryTagRecallTimeOut"`        //单位 ms
	ShippingFeeReqTimeOut                int `xml:"ShippingFeeReqTimeOut"`              //单位 ms
	StoreTagsReqTimeOut                  int `xml:"StoreTagsReqTimeOut"`                //单位 ms
	UdpGroupTimeOut                      int `xml:"UdpGroupTimeOut"`                    //单位 ms
	SearchStoreDishTimeOut               int `xml:"SearchStoreDishTimeOut"`             //单位 ms
	SearchStoreDishL3CategoryTimeOut     int `xml:"SearchStoreDishL3CategoryTimeOut"`   //单位 ms
	PeakModeDistanceTimeOut              int `xml:"PeakModeDistanceTimeOut"`            //单位 ms
	AffiliateClientTimeOut               int `xml:"AffiliateClientTimeOut"`             //单位 ms
	SearchBrandAggTimeOut                int `xml:"SearchBrandAggTimeOut"`              //单位 ms
}

type AbtestParamsSwitch struct {
	MultiFactorUseParamsPlatform        bool `xml:"MultiFactorUseParamsPlatform"`
	PredictCtrUseParamsPlatform         bool `xml:"PredictCtrUseParamsPlatform"`
	PredictCvrUseParamsPlatform         bool `xml:"PredictCvrUseParamsPlatform"`
	PredictUeUseParamsPlatform          bool `xml:"PredictUeUseParamsPlatform"`
	PredictRelevanceUseParamsPlatform   bool `xml:"PredictRelevanceUseParamsPlatform"`
	PredictDfUseParamsPlatform          bool `xml:"PredictDfUseParamsPlatform"`
	StoreA30ReRankUseParamsPlatform     bool `xml:"StoreA30ReRankUseParamsPlatform"`
	DishAnnRecallUseParamsPlatform      bool `xml:"DishAnnRecallUseParamsPlatform"`
	VectorEngineRecallUseParamsPlatform bool `xml:"VectorEngineRecallUseParamsPlatform"`
	RecallConfigUseParamsPlatform       bool `xml:"RecallConfigUseParamsPlatform"`
}

type NewAbConfig struct {
	Project            []string `xml:"Project"`
	SceneKey           []string `xml:"SceneKey"`
	Scene              string   `xml:"Scene"`
	KeyType            string   `xml:"KeyType"` // 支持三种模式，LayerID、LayerKey以及ExpName(默认使用LayerID)
	IsOpenNew          bool     `xml:"IsOpenNew"`
	TimeOut            int      `xml:"TimeOut"`
	InterceptSignature string   `xml:"InterceptSignature"`
}

// 不同等级 不同降级策略的配置
type LevelDowngradeConfigList struct {
	LevelDowngradeConfigs []LevelDowngradeConfig `xml:"LevelDowngradeConfig"`
}

// 同等级 降级策略的配置
type LevelDowngradeConfig struct {
	Level            string `xml:"Level"`
	DowngradeAds     bool   `xml:"DowngradeAds"`
	DowngradePredict bool   `xml:"DowngradePredict"`
}

type LevelDowngradeConfigControl struct {
	LevelDowngradeConfigMap map[string]*LevelDowngradeConfig
	sync.RWMutex
}

func (p *LevelDowngradeConfigControl) GetLevelDowngradeConfigControl(level string) *LevelDowngradeConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	if config, ok := p.LevelDowngradeConfigMap[level]; ok {
		return config
	}
	return nil
}

// 服务降级配置
type DowngradeServerConfig struct {
	CloseCodis             bool  `xml:"CloseCodis"`
	ClosePromotion         bool  `xml:"ClosePromotion"`
	CloseOptSort           bool  `xml:"CloseOptSort"`
	DowngradeQP            int32 `xml:"DowngradeQP"`
	DowngradeRelevance     int32 `xml:"DowngradeRelevance"`
	DowngradePredictCtr    int32 `xml:"DowngradePredictCtr"`
	DowngradePredictCvr    int32 `xml:"DowngradePredictCvr"`
	DowngradeDataServer    int32 `xml:"DowngradeDataServer"` // 正排
	DowngradePredictUE     int32 `xml:"DowngradePredictUE"`
	DowngradePredictDF     int32 `xml:"DowngradePredictDF"`
	DowngradePredictLtr    int32 `xml:"DowngradePredictLtr"`
	DowngradeDishSearcher  int32 `xml:"DowngradeDishSearcher"`
	DowngradeAds           int32 `xml:"DowngradeAds"`
	DowngradePredictCoarse int32 `xml:"DowngradePredictCoarse"` // 粗排降级
	// 正排返回doc数量的占比小于配置的值,则进行降级(不走过滤)
	DowngradeDataServerReturnSizePercent float64 `xml:"DowngradeDataServerReturnSizePercent"`
}

// 召回降级配置
type RecallDowngradeConfig struct {
	AllRecallSizeDowngrade float64             `xml:"AllRecallSizeDowngrade"`
	RecallSizeDowngrade    RecallSizeDowngrade `xml:"RecallSizeDowngrade"`
}

// 召回截断数降级配置, 配置大于 1 则表示该召回路关闭
type RecallSizeDowngrade struct {
	StoreRecallSize                            float64 `xml:"StoreRecallSize"`
	DishRecallSize                             float64 `xml:"DishRecallSize"`
	StoreIntentionRecallSize                   float64 `xml:"StoreIntentionRecallSize"`
	StoreWithDishRecallSize                    float64 `xml:"StoreWithDishRecallSize"`
	StoreCateRecallSize                        float64 `xml:"StoreCateRecallSize"`
	StoreNerAddTagRecallSize                   float64 `xml:"StoreNerAddTagRecallSize"`
	StoreNerOrTagRecallSize                    float64 `xml:"StoreNerOrTagRecallSize"`
	StoreNerReserveTagRecallSize               float64 `xml:"StoreNerReserveTagRecallSize"`
	StoreInterventionsRecallSize               float64 `xml:"StoreInterventionsRecallSize"`
	StoreInterventionsWithMerchantIdRecallSize float64 `xml:"StoreInterventionsWithMerchantIdRecallSize"`
	UnSettledMainCategoryRecallSize            float64 `xml:"UnSettledMainCategoryRecallSize"`
	UnSettledCategoryRecallSize                float64 `xml:"UnSettledCategoryRecallSize"`
	UnSettledDishRecallSize                    float64 `xml:"UnSettledDishRecallSize"`
	StoreNerRecallRecallSize                   float64 `xml:"StoreNerRecallRecallSize"`
	RewriteNerRecallRecallSize                 float64 `xml:"RewriteNerRecallRecallSize"`
	AnnRecallRecallSize                        float64 `xml:"AnnRecallRecallSize"`
	TextFieldRecallANDRecallSize               float64 `xml:"TextFieldRecallANDRecallSize"`
	TextFieldRecallORRecallSize                float64 `xml:"TextFieldRecallORRecallSize"`
	TextFieldRecallNearORRecallSize            float64 `xml:"TextFieldRecallNearORRecallSize"`
	QueryTagRecallSize                         float64 `xml:"QueryTagRecallSize"`
}

type IndexTrafficConfig struct {
	IndexAliasName string `xml:"IndexAliasName"`
	Switch         int    `xml:"Switch"`
}

type ABTestBlockControl struct {
	ABTestBlockMap map[string]bool
	sync.RWMutex
}

// 两个流量值是正整数,占比=流量值/二者总和
type ESSearchTrafficSplit struct {
	TrafficClient1            uint32             `xml:"TrafficClient1"`
	TrafficClient2            uint32             `xml:"TrafficClient2"`
	DoubleReadES              bool               `xml:"DoubleReadES"`
	StoreTrafficConfig        IndexTrafficConfig `xml:"StoreTrafficConfig"`
	DishTrafficConfig         IndexTrafficConfig `xml:"DishTrafficConfig"`
	HistoryOrderTrafficConfig IndexTrafficConfig `xml:"HistoryOrderTrafficConfig"`
	PrepaidTrafficConfig      IndexTrafficConfig `xml:"PrepaidTrafficConfig"`
	PrepaidDishTrafficConfig  IndexTrafficConfig `xml:"PrepaidDishTrafficConfig"`
}

var dishRecallConfMap map[string]map[string]float32

func GetDishRecallConf() map[string]map[string]float32 {
	mutex.RLock()
	defer mutex.RUnlock()
	return dishRecallConfMap
}

type ModelInfo struct {
	ModelName        string
	IsNeedPredict    bool
	IsPredictSuccess bool // 调用失败或者返回门店数不一致都不算
	RspModelInfo     *predictor.ModelInfo
}

var dfCallers map[string]string

var reRankExpWeightMap map[string]map[string]string
var reRankStgCaller map[string]int

func GetReRankExpWeight() map[string]map[string]string {
	mutex.RLock()
	defer mutex.RUnlock()
	return reRankExpWeightMap
}

func GetDFCallers() map[string]string {
	mutex.RLock()
	defer mutex.RUnlock()
	return dfCallers
}

// query string - ner 的映射关系表
var queryStringNerConfMap map[string][]string

func updateQueryStringNerConfMap(config *SearchApolloConfig) {
	if len(config.QueryStringNerMap) > 0 {
		queryStringNerConfMap = make(map[string][]string)
		if err := json.Unmarshal([]byte(config.QueryStringNerMap), &queryStringNerConfMap); err != nil {
			logkit.FromContext(context.Background()).WithError(err).Error("updateQueryStringNerConfMap failed", logkit.String("originJsonStr", config.QueryStringNerMap))
			return
		}
	}
}

// 召回 recallType 的映射关系表
var recallTypeMap map[string]int32

func updateRecallTypeMap(config *SearchApolloConfig) {
	if len(config.RecallTypeMappings) > 0 {
		recallTypeMap = make(map[string]int32)
		if err := json.Unmarshal([]byte(config.RecallTypeMappings), &recallTypeMap); err != nil {
			logkit.FromContext(context.Background()).WithError(err).Error("updateRecallTypeMap failed", logkit.String("originJsonStr", config.RecallTypeMappings))
			return
		}
	}
}

func GetRecallTypeMap() map[string]int32 {
	mutex.RLock()
	defer mutex.RUnlock()
	return recallTypeMap
}

func GetMaxQueryLen() int {
	if SearchApolloCfg.MaxQueryLen <= 0 {
		return 200
	}
	return SearchApolloCfg.MaxQueryLen
}

func GetMaxDishRecallStoreSize() int {
	if SearchApolloCfg.AdsRecallDishRecallStoreLength <= 0 {
		return 1000
	}
	return SearchApolloCfg.AdsRecallDishRecallStoreLength
}

func GetRoutingDishIndexShardCnt() int {
	if SearchApolloCfg.NewDishESRecallShardCnt > 0 {
		return SearchApolloCfg.NewDishESRecallShardCnt
	}
	return 10
}

func GetRoutingDishIndexShardHashPosMapping() []int {
	// 为了让门店尾号可以均匀打散到10个shards的mappings, 暂时不支持动态调整分片
	// 数组长度必须和 ShardCnt 相等
	hashMappings := []int{0, 1, 29, 3, 4, 5, 23, 7, 8, 12}
	if len(SearchApolloCfg.NewDishESRecallShardCntMappingStr) == 0 {
		return hashMappings
	}

	_mappings := make([]int, 0)
	b := strings.Split(SearchApolloCfg.NewDishESRecallShardCntMappingStr, ",")
	for _, i := range b {
		c, err := strconv.Atoi(i)
		if err != nil {
			logkit.FromContext(context.Background()).WithError(err).Error("GetRoutingDishIndexShardHashPosMapping Atoi failed", logkit.String("mappingStr", SearchApolloCfg.NewDishESRecallShardCntMappingStr))
			return hashMappings
		}
		_mappings = append(_mappings, c)
	}
	return _mappings
}
