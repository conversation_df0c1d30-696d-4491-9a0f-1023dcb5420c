package config

type DataServerFeatureConfig struct {
	FactorName   string `yaml:"factor_name" json:"factor_name"`
	Bid          uint64 `yaml:"bid" json:"bid"`
	FeatureId    uint64 `yaml:"feature_id" json:"feature_id"`
	KeyType      string `yaml:"key_type" json:"key_type"`   // ds接口传参的key类型，支持 PrepaidId, QueryPrepaidId
	DataKind     string `yaml:"data_kind" json:"data_kind"` // ds接口返回特征的类型，支持 Float, Int, String
	DefaultValue string `yaml:"default_value" json:"default_value"`
}

type FusionRankConfig struct {
	ExpString     string `yaml:"exp_string" json:"exp_string"`
	ExpParameters string `yaml:"exp_parameters" json:"exp_parameters"`
}
