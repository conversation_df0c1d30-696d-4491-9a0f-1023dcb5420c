package config

const ItemFeasTypeItemFeature = "ItemFeature"
const ItemFeasTypePrepaidFeature = "PrepaidFeature"

type ModelFactorConfig struct {
	ModelName        string             `xml:"ModelName"`
	FactorKeyConfigs []*FactorKeyConfig `xml:"FactorKeyConfig"` // 该模型的因子配置
	IsACKInstance    bool               `xml:"IsACKInstance"`   // 是否用于ACK实例
	IsRoughPredict   bool               `xml:"IsRoughPredict"`  // predict时是否带@rough_predict后缀
}

type FactorKeyConfig struct {
	FactorName     string  `xml:"FactorName"`     // 因子名称
	ModelName      string  `xml:"ModelName"`      // 模型名
	ScoreKey       string  `xml:"ScoreKey"`       // 模型返回分数key
	FactorKey      string  `xml:"FactorKey"`      // 打分因子
	DefaultScore   float64 `xml:"DefaultScore"`   // 默认分数
	MaxScore       float64 `xml:"MaxScore"`       // 最大分数
	MinScore       float64 `xml:"MinScore"`       // 最小分数
	ItemFeasType   string  `xml:"ItemFeasType"`   // predict 时ItemFeas的类型, 默认ItemFeature, SQS要用PrepaidFeature
	IsRoughPredict int     `xml:"IsRoughPredict"` // predict时是否带@rough_predict后缀
}
