package traceinfo

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
)

type HandlerType int

const (
	HandlerTypeSearch HandlerType = iota
	HandlerTypeSearchFood
	HandlerTypeSearchMart
	HandlerTypeSearchGlobal
	HandlerTypeSearchMainSite
	HandlerTypeSearchFoodTotalNum
	HandlerTypeSearchMartTotalNum
	HandlerTypeSearchCollection
	HandlerTypeSearchGlobalV1
	HandlerTypeSearchIdsStores
	HandlerTypeSearchIdsDishes
	HandlerTypeSearchStoreDishes
	HandlerTypeSearchIdsWeb
	HandlerTypeSearchIdsFoody
	HandlerTypeSearchStoresForAffiliate
	HandlerTypeSearchDishesForAffiliate
	HandlerTypeSearchHistoryOrder
	HandlerTypeSearchStoresWithListingDish
	HandlerTypeSearchCollectionWithListingDish
	HandlerTypeSearchDishesRecall
	HandlerTypeSearchPrepaidSKULandingPage
	HandlerTypeSearchPrepaidSKUForBag
)

func (h HandlerType) String() string {
	switch h {
	case HandlerTypeSearch:
		return "Search"
	case HandlerTypeSearchFood:
		return "SearchFood"
	case HandlerTypeSearchMart:
		return "SearchMart"
	case HandlerTypeSearchGlobal:
		return "SearchGlobal"
	case HandlerTypeSearchGlobalV1:
		return "SearchGlobalV1"
	case HandlerTypeSearchMainSite:
		return "SearchMainSite"
	case HandlerTypeSearchFoodTotalNum:
		return "SearchFoodTotalNum"
	case HandlerTypeSearchMartTotalNum:
		return "SearchMartTotalNum"
	case HandlerTypeSearchCollection:
		return "SearchCollection"
	case HandlerTypeSearchIdsStores:
		return "SearchIdsStores"
	case HandlerTypeSearchIdsDishes:
		return "SearchIdsDishes"
	case HandlerTypeSearchStoreDishes:
		return "SearchStoreDishes"
	case HandlerTypeSearchIdsWeb:
		return "SearchIdsWeb"
	case HandlerTypeSearchIdsFoody:
		return "SearchIdsFoody"
	case HandlerTypeSearchStoresForAffiliate:
		return "SearchStoresForAffiliate"
	case HandlerTypeSearchDishesForAffiliate:
		return "SearchDishesForAffiliate"
	case HandlerTypeSearchHistoryOrder:
		return "SearchHistoryOrder"
	case HandlerTypeSearchStoresWithListingDish:
		return "SearchStoresWithListingDish"
	case HandlerTypeSearchCollectionWithListingDish:
		return "SearchCollectionWithListingDish"
	case HandlerTypeSearchDishesRecall:
		return "SearchDishesRecall"
	case HandlerTypeSearchPrepaidSKULandingPage:
		return "SearchPrepaidSKULandingPage"
	case HandlerTypeSearchPrepaidSKUForBag:
		return "SearchPrepaidSKUForBag"
	default:
		return "SearchUnknown"
	}
}

// vn 类型需要重新调整下
func GetHandlerType(req *foodalgo_search.SearchRequest) HandlerType {
	searchType := HandlerTypeSearch
	if env.GetCID() == cid.VN {
		switch req.GetSceneId() {
		case 3:
			return HandlerTypeSearchGlobal
		case 4:
			return HandlerTypeSearchGlobalV1
		case 5:
			return HandlerTypeSearchIdsStores
		case 6:
			return HandlerTypeSearchIdsDishes
		case 7:
			return HandlerTypeSearchIdsWeb
		case 8:
			return HandlerTypeSearchIdsFoody
		}
		if req.GetSceneId() == 1 {
			if req.GetPageNum() == 0 {
				searchType = HandlerTypeSearchGlobal
			} else {
				searchType = HandlerTypeSearchFood
				if len(req.GetFilterType().GetCategoryType()) == 1 && req.GetFilterType().GetCategoryType()[0] == CategoryTypeMart {
					searchType = HandlerTypeSearchMart
				}
			}
		}
	}
	return searchType
}

func GetTotalNumHandlerType(req *foodalgo_search.SearchRequest) HandlerType {
	searchType := HandlerTypeSearchFoodTotalNum
	if len(req.GetFilterType().GetCategoryType()) == 1 && req.GetFilterType().GetCategoryType()[0] == CategoryTypeMart {
		searchType = HandlerTypeSearchMartTotalNum
	}
	return searchType
}

func GetTotalNumCategoryTypeStr(handlerType HandlerType) string {
	categoryType := "1001"
	if handlerType == HandlerTypeSearchMartTotalNum {
		categoryType = "1002"
	}
	return categoryType
}

func GetTraceContextSource(handlerType HandlerType, itemType string) string {
	if handlerType == HandlerTypeSearchCollection {
		if itemType == "store" {
			return "SearchCollection_FOOD_ALGO_TYPE_STORE"
		} else {
			return "SearchCollection_FOOD_ALGO_TYPE_DISH"
		}
	} else {
		if itemType == "store" {
			return "Search_FOOD_ALGO_TYPE_STORE"
		} else {
			return "Search_FOOD_ALGO_TYPE_DISH"
		}
	}
}
