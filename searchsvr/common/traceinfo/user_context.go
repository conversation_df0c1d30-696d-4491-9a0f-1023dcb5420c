package traceinfo

import (
	"context"
	"strconv"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/geohash"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_dishsearcher"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/timeutil"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/user_level"
)

type UserContext struct {
	//Query    string //真正用来搜索的搜索词  todo 删除
	//QueryRaw string //原始词 todo 删除

	Brand    string
	Model    string
	Os       string
	Wifi     int64
	DeviceId string

	Hour      int64
	IsHoliday int64
	DayOfWeek int64

	Longitude float32
	Latitude  float32
	GeoHash4  string
	GeoHash5  string
	GeoHash6  string

	UserLevel            []string
	UserInterestCategory []*o2oalgo_dishsearcher.KeywordInfo
	UserInterestTag      []*o2oalgo_dishsearcher.KeywordInfo
}

func buildUserContext(ctx context.Context, req *foodalgo_search.SearchRequest, traceInfo *TraceInfo) {
	//traceInfo.UserContext.QueryRaw = req.GetKeyword() // 原本有两次赋值，本次保留原始词，不做任何操作，包括小写转换
	clientInfo := req.GetClientExtraInfo()
	if clientInfo != nil && clientInfo.GetDeviceId() != "" {
		wifi, err := strconv.Atoi(clientInfo.GetWifi())
		if err != nil {
			wifi = -1
		}
		traceInfo.UserContext.Brand = clientInfo.GetBrand()
		traceInfo.UserContext.Model = clientInfo.GetModel()
		traceInfo.UserContext.Os = clientInfo.GetOs()
		traceInfo.UserContext.Wifi = int64(wifi)
		traceInfo.UserContext.DeviceId = clientInfo.GetDeviceId()
	}

	today := timeutil.GetCIDWeekDay(timeutil.Now())
	hour := timeutil.GetCIDTime(timeutil.Now()).Hour()
	traceInfo.UserContext.Hour = int64(hour)
	traceInfo.UserContext.DayOfWeek = int64(today)
	if today == time.Saturday || today == time.Sunday {
		traceInfo.UserContext.IsHoliday = 1 // 默认是 0
	}

	traceInfo.UserContext.Latitude = req.GetLatitude()
	traceInfo.UserContext.Longitude = req.GetLongitude()
	traceInfo.UserContext.GeoHash4 = geohash.RedisEncodeWithPrecision(float64(req.GetLatitude()), float64(req.GetLongitude()), 4)
	traceInfo.UserContext.GeoHash5 = geohash.RedisEncodeWithPrecision(float64(req.GetLatitude()), float64(req.GetLongitude()), 5)
	traceInfo.UserContext.GeoHash6 = geohash.RedisEncodeWithPrecision(float64(req.GetLatitude()), float64(req.GetLongitude()), 6)

	traceInfo.UserContext.UserLevel = user_level.LevelDict.GetUserLevel(req.GetBuyerId())

}

func buildUserContextForAffiliate(ctx context.Context, req *foodalgo_search.SearchStoresAffiliateReq, traceInfo *TraceInfo) {
	clientInfo := req.GetClientExtraInfo()
	if clientInfo != nil && clientInfo.GetDeviceId() != "" {
		wifi, err := strconv.Atoi(clientInfo.GetWifi())
		if err != nil {
			wifi = -1
		}
		traceInfo.UserContext.Brand = clientInfo.GetBrand()
		traceInfo.UserContext.Model = clientInfo.GetModel()
		traceInfo.UserContext.Os = clientInfo.GetOs()
		traceInfo.UserContext.Wifi = int64(wifi)
		traceInfo.UserContext.DeviceId = clientInfo.GetDeviceId()
	}

	today := timeutil.GetCIDWeekDay(timeutil.Now())
	hour := timeutil.GetCIDTime(timeutil.Now()).Hour()
	traceInfo.UserContext.Hour = int64(hour)
	traceInfo.UserContext.DayOfWeek = int64(today)
	if today == time.Saturday || today == time.Sunday {
		traceInfo.UserContext.IsHoliday = 1 // 默认是 0
	}
	// affiliate 默认用0.0，0.0 计算geo hash
	traceInfo.UserContext.GeoHash4 = geohash.RedisEncodeWithPrecision(0.0, 0.0, 4)
	traceInfo.UserContext.GeoHash5 = geohash.RedisEncodeWithPrecision(0.0, 0.0, 5)
	traceInfo.UserContext.GeoHash6 = geohash.RedisEncodeWithPrecision(0.0, 0.0, 6)
}

// todo 统一方法, 与req 隔离
func buildUserContextForStoreListing(ctx context.Context, req *foodalgo_search.SearchStoresWithListingDishReq, traceInfo *TraceInfo) {
	clientInfo := req.GetClientExtraInfo()
	if clientInfo != nil && clientInfo.GetDeviceId() != "" {
		wifi, err := strconv.Atoi(clientInfo.GetWifi())
		if err != nil {
			wifi = -1
		}
		traceInfo.UserContext.Brand = clientInfo.GetBrand()
		traceInfo.UserContext.Model = clientInfo.GetModel()
		traceInfo.UserContext.Os = clientInfo.GetOs()
		traceInfo.UserContext.Wifi = int64(wifi)
		traceInfo.UserContext.DeviceId = clientInfo.GetDeviceId()
	}

	today := timeutil.GetCIDWeekDay(timeutil.Now())
	hour := timeutil.GetCIDTime(timeutil.Now()).Hour()
	traceInfo.UserContext.Hour = int64(hour)
	traceInfo.UserContext.DayOfWeek = int64(today)
	if today == time.Saturday || today == time.Sunday {
		traceInfo.UserContext.IsHoliday = 1 // 默认是 0
	}

	traceInfo.UserContext.Latitude = float32(req.GetLatitude())
	traceInfo.UserContext.Longitude = float32(req.GetLongitude())
	traceInfo.UserContext.GeoHash4 = geohash.RedisEncodeWithPrecision(req.GetLatitude(), req.GetLongitude(), 4)
	traceInfo.UserContext.GeoHash5 = geohash.RedisEncodeWithPrecision(req.GetLatitude(), req.GetLongitude(), 5)
	traceInfo.UserContext.GeoHash6 = geohash.RedisEncodeWithPrecision(req.GetLatitude(), req.GetLongitude(), 6)
}

func buildCommonUserContext(ctx context.Context, traceInfo *TraceInfo, clientInfo *foodalgo_search.ClientDeviceInfo, long, lat float64) {
	if clientInfo != nil && clientInfo.GetDeviceId() != "" {
		wifi, err := strconv.Atoi(clientInfo.GetWifi())
		if err != nil {
			wifi = -1
		}
		traceInfo.UserContext.Brand = clientInfo.GetBrand()
		traceInfo.UserContext.Model = clientInfo.GetModel()
		traceInfo.UserContext.Os = clientInfo.GetOs()
		traceInfo.UserContext.Wifi = int64(wifi)
		traceInfo.UserContext.DeviceId = clientInfo.GetDeviceId()
	}

	today := timeutil.GetCIDWeekDay(timeutil.Now())
	hour := timeutil.GetCIDTime(timeutil.Now()).Hour()
	traceInfo.UserContext.Hour = int64(hour)
	traceInfo.UserContext.DayOfWeek = int64(today)
	if today == time.Saturday || today == time.Sunday {
		traceInfo.UserContext.IsHoliday = 1 // 默认是 0
	}

	traceInfo.UserContext.Latitude = float32(lat)
	traceInfo.UserContext.Longitude = float32(long)
	traceInfo.UserContext.GeoHash4 = geohash.RedisEncodeWithPrecision(lat, long, 4)
	traceInfo.UserContext.GeoHash5 = geohash.RedisEncodeWithPrecision(lat, long, 5)
	traceInfo.UserContext.GeoHash6 = geohash.RedisEncodeWithPrecision(lat, long, 6)
}
