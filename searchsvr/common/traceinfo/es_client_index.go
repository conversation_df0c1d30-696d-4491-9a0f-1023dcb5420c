package traceinfo

import (
	"context"
	"fmt"
	"math/rand"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

const (
	ESClient1 = "es-client-1"
	ESClient2 = "es-client-2"

	StoreES        = "store"
	DishES         = "dish"
	HistoryOrderES = "historyorder"
	PrepaidES      = "prepaid"
	PrepaidDishES  = "prepaid_dish"
)

func buildEsClientAndIndex(ctx context.Context, traceInfo *TraceInfo) {
	defer func() {
		storeClientIndex := traceInfo.SearchESClient + traceInfo.StoreIndexIndexAlias
		dishClientIndex := traceInfo.SearchESClient + traceInfo.DishIndexIndexAlias
		historyOrderClientIndex := traceInfo.SearchESClient + traceInfo.HistoryOrderIndexIndexAlias
		prepaidIndex := traceInfo.SearchESClient + traceInfo.PrepaidIndexAlias
		prepaidDishIndex := traceInfo.SearchESClient + traceInfo.PrepaidDishIndexAlias
		logger.MyDebug(ctx, traceInfo.IsDebug, "search es client and index",
			zap.String("store index", storeClientIndex),
			zap.String("dish index", dishClientIndex),
			zap.String("historyOrder index", historyOrderClientIndex),
			zap.String("prepaid index", prepaidIndex),
			zap.String("prepaidDish index", prepaidDishIndex),
		)
		_ = metric_reporter.ReportESSearchClient(1, traceInfo.HandlerType.String(), &storeClientIndex)
		_ = metric_reporter.ReportESSearchClient(1, traceInfo.HandlerType.String(), &dishClientIndex)
		_ = metric_reporter.ReportESSearchClient(1, traceInfo.HandlerType.String(), &historyOrderClientIndex)
		_ = metric_reporter.ReportESSearchClient(1, traceInfo.HandlerType.String(), &prepaidIndex)
		_ = metric_reporter.ReportESSearchClient(1, traceInfo.HandlerType.String(), &prepaidDishIndex)
	}()
	traceInfo.SearchESClient = selectEsClient()
	traceInfo.StoreIndexIndexAlias = selectEsIndexAlias(ctx, traceInfo, StoreES, apollo.SearchApolloCfg.ESSearchTrafficSplit.StoreTrafficConfig)
	traceInfo.DishIndexIndexAlias = selectEsIndexAlias(ctx, traceInfo, DishES, apollo.SearchApolloCfg.ESSearchTrafficSplit.DishTrafficConfig)
	traceInfo.HistoryOrderIndexIndexAlias = selectEsIndexAlias(ctx, traceInfo, HistoryOrderES, apollo.SearchApolloCfg.ESSearchTrafficSplit.HistoryOrderTrafficConfig)
	traceInfo.PrepaidIndexAlias = selectEsIndexAlias(ctx, traceInfo, PrepaidES, apollo.SearchApolloCfg.ESSearchTrafficSplit.PrepaidTrafficConfig)
	traceInfo.PrepaidDishIndexAlias = selectEsIndexAlias(ctx, traceInfo, PrepaidDishES, apollo.SearchApolloCfg.ESSearchTrafficSplit.PrepaidDishTrafficConfig)
}

func selectEsClient() string {
	t1 := int(apollo.SearchApolloCfg.ESSearchTrafficSplit.TrafficClient1)
	t2 := int(apollo.SearchApolloCfg.ESSearchTrafficSplit.TrafficClient2)
	sum := t1 + t2
	// 未配置Apollo，直接跳过
	if t1 == 0 && t2 == 0 {
		return ESClient1
	}
	// 不走client2,全部走client1
	if t2 == 0 {
		return ESClient1
	}
	// 不走client1,全部走client2
	if t1 == 0 {
		return ESClient2
	}
	r := rand.Intn(sum) // 0 ~ sum-1
	if r >= t1 {
		return ESClient2
	}
	return ESClient1
}

func IsDishUseRouting(traceInfo *TraceInfo, indexType string) bool {
	if indexType == DishES && (traceInfo.HandlerType == HandlerTypeSearchStoresWithListingDish || traceInfo.HandlerType == HandlerTypeSearchCollectionWithListingDish || traceInfo.HandlerType == HandlerTypeSearchDishesRecall) {
		if env.GetEnv() == "live" || env.GetEnv() == "liveish" {
			return true
		}
	}
	return false
}

func selectEsIndexAlias(ctx context.Context, traceInfo *TraceInfo, indexType string, indexTrafficConfig apollo.IndexTrafficConfig) string {
	var indexName string
	switch indexType {
	case StoreES:
		indexName = fmt.Sprintf("foodalgo_new_%v_%v_store_search_index", env.GetEnv(), env.GetCID())
	case DishES:
		indexName = fmt.Sprintf("foodalgo_new_%v_%v_dish_search_index", env.GetEnv(), env.GetCID())
		if IsDishUseRouting(traceInfo, indexType) {
			indexName = fmt.Sprintf("foodalgo_dataplatform_%v_%v_dish_search_index_routing", env.GetEnv(), env.GetCID())
		}
	case HistoryOrderES:
		indexName = fmt.Sprintf("foodalgo_dataplatform_%v_%v_store_search_index_history", env.GetEnv(), env.GetCID())
	case PrepaidES:
		indexName = fmt.Sprintf("foodalgo_dataplatform_%v_%v_dish_search_index_prepaid", env.GetEnv(), env.GetCID())
	case PrepaidDishES:
		indexName = fmt.Sprintf("foodalgo_dataplatform_%v_%v_dish_search_index_sqs_dish", env.GetEnv(), env.GetCID())
	default:
		logkit.FromContext(ctx).Error("error index type, must check!!!", zap.String("indexType", indexType))
		indexName = fmt.Sprintf("foodalgo_new_%v_%v_store_search_index", env.GetEnv(), env.GetCID())
	}
	return indexName
}

func GetESClientFromPool(ctx context.Context, traceInfo *TraceInfo, indexType string) *es.ESBase {
	var indexAlias string
	switch indexType {
	case StoreES:
		indexAlias = traceInfo.StoreIndexIndexAlias
	case DishES:
		indexAlias = traceInfo.DishIndexIndexAlias
	case HistoryOrderES:
		indexAlias = traceInfo.HistoryOrderIndexIndexAlias
	case PrepaidES:
		indexAlias = traceInfo.PrepaidIndexAlias
	case PrepaidDishES:
		indexAlias = traceInfo.PrepaidDishIndexAlias
	default:
		logkit.FromContext(ctx).Error("error index type, must check!!!", zap.String("indexType", indexType))
		indexAlias = traceInfo.StoreIndexIndexAlias
	}

	clientAndIndex := traceInfo.SearchESClient + indexAlias

	if es.ESClientPool[clientAndIndex] == nil {
		es.ESClientPoolLock.Lock()
		if es.ESClientPool[clientAndIndex] == nil {
			esClient := es.ESClient1
			if traceInfo.SearchESClient == ESClient2 {
				esClient = es.ESClient2
			}
			eSBase := es.NewES(indexAlias, esClient)
			es.ESClientPool[clientAndIndex] = eSBase
		}
		es.ESClientPoolLock.Unlock()
	}
	return es.ESClientPool[clientAndIndex]
}
