package traceinfo

import (
	"context"

	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
)

const (
	// 各阶段门店数
	StoreLenMerge                    = "StoreLenMerge"
	StoreLenMergeNer                 = "StoreLenMergeNer"
	StoreLenMergeI2I                 = "StoreLenMergeI2I"
	StoreLenFilling                  = "StoreLenFilling"
	StoreLenFilter                   = "StoreLenFilter"
	StoreLenFilterDistanceVN         = "StoreLenFilterDistanceVN"
	StoreLenFilterDistanceVNMainSite = "StoreLenFilterDistanceVNMainSite"
	StoreLenFilterDuplicateBrand     = "StoreLenFilterDuplicateBrand"
	StoreLenFinal                    = "StoreLenFinal"
	StoreLenMixer                    = "StoreLenMixer"
	StoreLenIntervention             = "StoreLenIntervention"
	StoreLenAds                      = "StoreLenAds"
	StoreLenCoarseRank               = "StoreLenCoarseRank" // 粗排
	StoreLenLWDistinct               = "StoreLenLWDistinct"
	PrepaidLenFinal                  = "PrepaidLenFinal"

	// 某些情况下的门店数量
	StoreCntOpeningStoreBeforeFilter = "StoreCntOpeningStoreBeforeFilter"
	StoreCntOpeningStoreAfterFilter  = "StoreCntOpeningStoreAfterFilter"
	StoreCntNearDistanceBeforeFilter = "StoreCntNearDistanceBeforeFilter"
	StoreCntNearDistanceAfterFilter  = "StoreCntNearDistanceAfterFilter"
	StoreCntUserFsI2I                = "StoreCntUserFsI2I"

	AvgDistanceBeforeFilter = "AvgDistanceBeforeFilter"
	AvgDistanceAfterFilter  = "AvgDistanceAfterFilter"
	AvgDistanceAtFinal      = "AvgDistanceAtFinal"

	DishLenRecall                     = "DishLenRecall"
	DishLenRecallFromES               = "DishLenRecallFromES"
	DishLenRecallFromFS               = "DishLenRecallFromFS"
	DishLenRecallFromDS               = "DishLenRecallFromDS"
	DishLenRecallFromDishSearcher     = "DishLenRecallFromDishSearcher"
	DishLenBestSelling                = "DishLenBestSelling"
	DishLenFinal                      = "DishLenFinal"
	StoreDishLenRecallFromES          = "StoreDishLenRecallFromES"
	StoreCategoryLenRecallFromES      = "StoreCategoryLenRecallFromES"
	StoreLenRecallFromESAffiliate     = "StoreLenRecallFromESAffiliate"
	StoreDishLenRecallFromESAffiliate = "StoreDishLenRecallFromESAffiliate"
	BrandLenRecallFromESAffiliate     = "BrandLenRecallFromESAffiliate"

	// 历史订单
	HistoryOrderLenFinal = "HistoryOrderLenFinal"

	// SQS
	PrepaidLenRecallFromES             = "PrepaidLenRecallFromES"
	PrepaidLenRecallFromDataServer     = "PrepaidLenRecallFromDataServer"
	PrepaidLenMergeAndTruncatePrepaids = "PrepaidLenMergeAndTruncatePrepaids"
	PrepaidLenFilling                  = "PrepaidLenFilling"
	PrepaidLenFilter                   = "PrepaidLenFilter"
	PrepaidLenRank                     = "PrepaidLenRank"
	PrepaidLenReRank                   = "PrepaidLenReRank"
)

var HistogramStoreLengthBuckets = []float64{
	0, 10, 20, 30, 40, 50, 80, 100, 200, 300, 350, 400, 450, 500, 550, 600, 700, 800, 900, 1000, 1500, 1600, 1800, 1900, 2000, 2100, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 12000, 13000, 14000, 15000, 16000, 17000, 18000, 19000, 20000, 21000, 22000, 23000, 24000, 25000, 26000, 27000, 28000, 29000, 30000, 31000, 32000, 33000, 34000, 35000, 36000, 37000, 38000, 39000, 40000, 50000, 60000, 70000, 80000, 90000, 100000, 110000, 120000, 130000, 140000, 150000, 200000,
}

func (t *TraceInfo) AddPhraseStoreLength(ctx context.Context, phraseType string, itemList int) {
	reporter.ReportHistogramWithBuckets(reporter.ReportSearchRecallDocNumMetricName,
		float64(itemList), HistogramStoreLengthBuckets,
		reporter2.Label{Key: "doc", Val: phraseType},
		reporter2.Label{Key: "handler", Val: t.HandlerType.String()},
		reporter2.Label{Key: "pipeline", Val: t.PipelineType.String()})

	t.SetPhraseStoreLength(phraseType, itemList)
}
