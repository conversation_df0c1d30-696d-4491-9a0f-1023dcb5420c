package decision

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsUseSearchDishCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishCache: DowngradeServerConfig.CloseCodis is true, close cache")
		return false
	}
	if apollo.SearchApolloCfg.UserCacheExpireSeconds == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishCache: UserCacheExpireSeconds is 0, close cache")
		return false
	}
	if apollo.SearchApolloCfg.CloseSearchDishesCache {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishCache: CloseSearchDishesCache is true, close cache")
		return false
	}
	if traceInfo.IsDebug {
		return false
	}
	return true
}

func IsUseSearchDishL3CategoryCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishCache: DowngradeServerConfig.CloseCodis is true, close cache")
		return false
	}
	if apollo.SearchApolloCfg.UserCacheExpireSeconds == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishCache: UserCacheExpireSeconds is 0, close cache")
		return false
	}
	if apollo.SearchApolloCfg.CloseSearchDishL3CategoryCache {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishCache: CloseSearchDishL3CategoryCache is true, close cache")
		return false
	}
	if traceInfo.IsDebug {
		return false
	}
	return true
}

func IsUseSearchStoreAffiliateCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchStoreAffiliateCache: DowngradeServerConfig.CloseCodis is true, close cache")
		return false
	}
	if apollo.SearchApolloCfg.AffiliateCacheExpireSeconds == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchStoreAffiliateCache: AffiliateCacheExpireSeconds is 0, close cache")
		return false
	}
	if apollo.SearchApolloCfg.CloseSearchStoresAffiliateCache {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchStoreAffiliateCache: CloseSearchStoresAffiliateCache is true, close cache")
		return false
	}
	if traceInfo.IsDebug {
		return false
	}
	return true
}

func IsUseSearchDishAffiliateCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishAffiliateCache: DowngradeServerConfig.CloseCodis is true, close cache")
		return false
	}
	if apollo.SearchApolloCfg.AffiliateCacheExpireSeconds == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishAffiliateCache: AffiliateCacheExpireSeconds is 0, close cache")
		return false
	}
	if apollo.SearchApolloCfg.CloseSearchDishesAffiliateCache {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseSearchDishAffiliateCache: CloseSearchDishesAffiliateCache is true, close cache")
		return false
	}
	if traceInfo.IsDebug {
		return false
	}
	return true
}

func IsUseSearchStoresWithListingDishCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		return false
	}
	if traceInfo.IsDebug {
		return false
	}
	return true
}

func IsUseResultCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		return false
	}
	if traceInfo.IsDebug {
		return false
	}
	return true
}
