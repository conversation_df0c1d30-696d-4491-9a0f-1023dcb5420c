package debuginfo

import (
	"context"
	"encoding/json"
	"fmt"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
	"github.com/golang/protobuf/proto"
)

type CombineDebugInfo struct {
	RequestHelperInfo   *RequestHelperInfo   `json:"request_helper_info,omitempty"`
	NormalStoreInfos    model.StoreInfos     `json:"normal_store_infos,omitempty"`
	AdsStoreInfos       model.StoreInfos     `json:"ads_store_infos,omitempty"`
	MixerStoreInfos     model.StoreInfos     `json:"mixer_store_infos,omitempty"`
	DishInfos           model.DishInfos      `json:"dish_infos,omitempty"`
	PrepaidInfos        model.PrepaidInfos   `json:"prepaid_infos,omitempty"`
	PipelineProcessInfo *PipelineProcessInfo `json:"pipeline_process_info,omitempty"`
}

type RequestHelperInfo struct {
	//OriginRequest            *foodalgo_search.SearchRequest   `json:"origin_request,omitempty"` // 原始请求,不进行修改赋值。
	QPResult                 *traceinfo.QPResult              `json:"qp_result,omitempty"`
	OptIntervention          *traceinfo.OptIntervention       `json:"opt_intervention,omitempty"`
	PredefinedKeyword        string                           `json:"predefined_keyword,omitempty"`
	IsPredefineKeyword       bool                             `json:"is_predefine_keyword,omitempty"`
	IsFromCache              bool                             `json:"is_from_cache,omitempty"`
	DynamicFactor            []float32                        `json:"dynamic_factor,omitempty"`
	QueryCateIntents         []string                         `json:"query_cate_intents,omitempty"`
	Ner                      []string                         `json:"ner,omitempty"`
	NerAndRecallResult       []string                         `json:"ner_and_recall_result,omitempty"`
	NerOrRecallResult        []string                         `json:"ner_or_recall_result,omitempty"`
	RewriteResult            []*foodalgo_search.RewriteResult `json:"rewrite_result,omitempty"`
	TermWeight               string                           `json:"term_weight,omitempty"`
	QueryStringNerTermWeight string                           `json:"query_string_ner_term_weight,omitempty"`
	IsStoreTopHit            bool                             `json:"is_store_top_hit,omitempty"`
	StoreTopKeyword          string                           `json:"store_top_keyword,omitempty"`
	AbtestParams             map[string]string                `json:"abtest_params"`
}

type PipelineProcessInfo struct { // 各个阶段的核心指标：过滤、耗时、召回配置化等
	RecallConfigurationDataParams   map[string]interface{} // 召回配置化的，当前请求的数据集
	PhraseTimeCost                  map[string]string      // 记录各个阶段的耗时
	PhraseStoreLength               map[string]int         // 记录各个阶段的门店数量
	PhraseStoreInfos                map[string][]string    // 记录各个阶段的门店的ids,scores  demo like: {storeId}#{esScore}
	PhraseStoreESDsl                map[string]string      // 记录各个阶段的es store dsl，如果是来自 es 召回的话
	PhraseDishESDsl                 map[string]string      // 记录各个阶段的es dish dsl，如果是来自 es 召回的话
	PhraseOrderESDsl                []string               // 记录各个阶段的es order dsl，如果是来自 es 召回的话
	FilteredStores                  map[string][]*traceinfo.FilteredStore
	RecallsStoreHitAbtest           []string // 记录hit abtest 召回的路
	RecallsStoreNotHitAbtest        []string // 记录 not-hit abtest 召回的路
	RecallsStoreFinal               []string // 记录符合 condition 召回的路
	RecallsDishHitAbtest            []string // 记录hit abtest 召回的路 for dish
	RecallsDishNotHitAbtest         []string // 记录 not-hit abtest 召回的路 for dish
	RecallsDishFinal                []string // 记录符合 condition 召回的路 for dish
	RecallsDishConditionOkButGiveUp []string // 记录符合 condition 召回的路 for dish - 但是因为限制一路只能放弃
	RecallsOrdersFinal              []string // 记录符合 condition 召回的路
	PredictConfig                   *traceinfo.PredictConfig
	EmbeddingSuppressRule           []string
	ListWistStoreLists              []*model.StoreList
	ContextFeature                  *food.ContextFeature // 模型预估的context特征
}

func InitCombineDebugInfo(traceInfo *traceinfo.TraceInfo) *CombineDebugInfo {
	if traceInfo.IsDebug == false {
		return nil
	}
	debugInfo := &CombineDebugInfo{
		RequestHelperInfo: &RequestHelperInfo{
			//OriginRequest: traceInfo.OriginRequest,
			QPResult: &traceinfo.QPResult{OtherSegments: &traceinfo.OtherSegments{}},
		},
		NormalStoreInfos:    make(model.StoreInfos, 0),
		AdsStoreInfos:       make(model.StoreInfos, 0),
		MixerStoreInfos:     make(model.StoreInfos, 0),
		PrepaidInfos:        make(model.PrepaidInfos, 0),
		PipelineProcessInfo: &PipelineProcessInfo{},
	}
	return debugInfo
}

func (debugInfo *CombineDebugInfo) FillProcessInfo(traceInfo *traceinfo.TraceInfo) {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return
	}
	// 请求级别
	debugInfo.RequestHelperInfo.QPResult = traceInfo.QPResult
	debugInfo.RequestHelperInfo.OptIntervention = traceInfo.OptIntervention
	debugInfo.RequestHelperInfo.PredefinedKeyword = traceInfo.PredefineKeyword
	debugInfo.RequestHelperInfo.IsPredefineKeyword = traceInfo.IsPredefineKeyword
	debugInfo.RequestHelperInfo.IsStoreTopHit = traceInfo.IsStoreTopHit
	debugInfo.RequestHelperInfo.StoreTopKeyword = traceInfo.StoreTopKeyword

	// 请求因子
	if traceInfo.PredictConfig != nil && len(traceInfo.PredictConfig.DyFactorList) > 0 {
		nLen := len(traceInfo.PredictConfig.DyFactorList)
		debugInfo.RequestHelperInfo.DynamicFactor = make([]float32, nLen)
		for i := 0; i < nLen; i++ {
			debugInfo.RequestHelperInfo.DynamicFactor[i] = float32(traceInfo.PredictConfig.DyFactorList[i])
		}
	}

	// category
	if len(traceInfo.QPResult.CategoryIntentions) > 0 {
		queryCateIntents := make([]string, 0)
		for _, val := range traceInfo.QPResult.CategoryIntentions {
			queryCateIntents = append(queryCateIntents, fmt.Sprintf("%s|%s|%f", val.GetLevel1Name(), val.GetLevel2Name(), val.GetScore()))
		}
		debugInfo.RequestHelperInfo.QueryCateIntents = queryCateIntents
	}
	// ner
	if len(traceInfo.QPResult.NerResult) > 0 {
		tmpNer := make([]string, 0)
		for _, val := range traceInfo.QPResult.NerResult {
			tmpNer = append(tmpNer, val.String())
		}
		debugInfo.RequestHelperInfo.Ner = tmpNer
	}
	if len(traceInfo.QPResult.NerAndRecallResult) > 0 {
		tmpNerAndRecall := make([]string, 0)
		for _, val := range traceInfo.QPResult.NerAndRecallResult {
			tmpNerAndRecall = append(tmpNerAndRecall, val.String())
		}
		debugInfo.RequestHelperInfo.NerAndRecallResult = tmpNerAndRecall
	}
	if len(traceInfo.QPResult.NerOrRecallResult) > 0 {
		tmpNerOrRecall := make([]string, 0)
		for _, val := range traceInfo.QPResult.NerOrRecallResult {
			tmpNerOrRecall = append(tmpNerOrRecall, val.String())
		}
		debugInfo.RequestHelperInfo.NerOrRecallResult = tmpNerOrRecall
	}

	// rewrite
	rewriteResult := make([]*foodalgo_search.RewriteResult, 0)
	for _, rewrite := range traceInfo.QPResult.RewriteNerOriginResult {
		rewriteNer := make([]string, 0)

		if rewrite.QueryRewriteNer != nil && len(rewrite.GetQueryRewriteNer()) > 0 {
			for _, val := range rewrite.QueryRewriteNer {
				rewriteNer = append(rewriteNer, val.String())
			}
		}
		// 这样写可以防止空指针问题
		rewriteResult = append(rewriteResult, &foodalgo_search.RewriteResult{
			RewriteQuery: proto.String(rewrite.GetRewriteQuery()),
			RewriteNer:   rewriteNer,
			RewriteScoreFactor: &foodalgo_search.RewriteScoreFactor{
				Qv:          proto.Float32(rewrite.GetQueryRewriteScore().GetQv()),
				Ctcvr:       proto.Float32(rewrite.GetQueryRewriteScore().GetCtcvr()),
				EntityScore: proto.Float32(rewrite.GetQueryRewriteScore().GetEntityScore()),
			},
		})
	}
	debugInfo.RequestHelperInfo.RewriteResult = rewriteResult
	// 打印所以实验参数到debug信息中
	debugInfo.RequestHelperInfo.AbtestParams = traceInfo.AbParamClient.AbParamClient.GetAllParams()

	// 中间过程级别
	debugInfo.PipelineProcessInfo.RecallConfigurationDataParams = traceInfo.RecallConfigurationDataParams
	debugInfo.PipelineProcessInfo.PhraseTimeCost = make(map[string]string, 0)
	for k, v := range traceInfo.PhraseTimeCost {
		debugInfo.PipelineProcessInfo.PhraseTimeCost[string(k)] = v.String()
	}

	debugInfo.PipelineProcessInfo.FilteredStores = traceInfo.FilteredStores
	debugInfo.PipelineProcessInfo.PhraseStoreLength = traceInfo.PhraseStoreLength
	debugInfo.PipelineProcessInfo.PhraseStoreInfos = traceInfo.PhraseStoreInfos
	debugInfo.PipelineProcessInfo.PhraseStoreESDsl = traceInfo.PhraseStoreESDsl
	debugInfo.PipelineProcessInfo.PhraseDishESDsl = traceInfo.PhraseDishESDsl
	debugInfo.PipelineProcessInfo.PhraseOrderESDsl = util.SyncMapToList(traceInfo.PhraseOrderESDsl)
	debugInfo.PipelineProcessInfo.PredictConfig = traceInfo.PredictConfig
	debugInfo.PipelineProcessInfo.RecallsStoreNotHitAbtest = traceInfo.RecallsStoreNotHitAbtest
	debugInfo.PipelineProcessInfo.RecallsStoreHitAbtest = traceInfo.RecallsStoreHitAbtest
	debugInfo.PipelineProcessInfo.RecallsStoreFinal = traceInfo.RecallsStoreFinal
	debugInfo.PipelineProcessInfo.RecallsDishNotHitAbtest = util.DeduplicateStringSlice(traceInfo.RecallsDishNotHitAbtest)
	debugInfo.PipelineProcessInfo.RecallsDishHitAbtest = util.DeduplicateStringSlice(traceInfo.RecallsDishHitAbtest)
	debugInfo.PipelineProcessInfo.RecallsDishFinal = util.DeduplicateStringSlice(traceInfo.RecallsDishFinal)
	debugInfo.PipelineProcessInfo.RecallsDishConditionOkButGiveUp = util.DeduplicateStringSlice(traceInfo.RecallsDishConditionOkButGiveUp)
	debugInfo.PipelineProcessInfo.EmbeddingSuppressRule = traceInfo.EmbeddingSuppressRule
	debugInfo.PipelineProcessInfo.ContextFeature = traceInfo.ContextFeature
	debugInfo.PipelineProcessInfo.RecallsOrdersFinal = traceInfo.RecallsOrdersFinal
}

func (debugInfo *CombineDebugInfo) FillProcessInfoWithListWise(traceInfo *traceinfo.TraceInfo, storeLists []*model.StoreList) {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return
	}
	debugInfo.PipelineProcessInfo.ListWistStoreLists = storeLists
}

func (debugInfo *CombineDebugInfo) FillNormalStores(traceInfo *traceinfo.TraceInfo, normalStores model.StoreInfos) {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return
	}
	if traceInfo.SearchDebugReq.GetDebugSwitch().GetReturnStoresType() == foodalgo_search.DebugReturnStoresType_ReturnNormalStores {
		res := make([]*model.StoreInfo, 0, len(normalStores))
		for _, store := range normalStores {
			if store.ItemFeature.GetCIStoreType() == model.StoreTypeAds {
				continue
			}
			res = append(res, store)
		}
		debugInfo.NormalStoreInfos = debugInfo.handleCutPage(traceInfo, res)
	}
}

func (debugInfo *CombineDebugInfo) FillAdsStores(traceInfo *traceinfo.TraceInfo, ads model.StoreInfos) {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return
	}
	if traceInfo.SearchDebugReq.GetDebugSwitch().GetReturnStoresType() == foodalgo_search.DebugReturnStoresType_ReturnAdsStores {
		debugInfo.AdsStoreInfos = debugInfo.handleCutPage(traceInfo, ads)
	}
}

func (debugInfo *CombineDebugInfo) FillMixStores(traceInfo *traceinfo.TraceInfo, mix model.StoreInfos) {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return
	}
	if traceInfo.SearchDebugReq.GetDebugSwitch().GetReturnStoresType() == foodalgo_search.DebugReturnStoresType_ReturnMixStores {
		debugInfo.MixerStoreInfos = debugInfo.handleCutPage(traceInfo, mix)
	}
}

func (debugInfo *CombineDebugInfo) handleCutPage(traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}

	// 分页
	pageNum := traceInfo.TraceRequest.PageNum
	pageSize := traceInfo.TraceRequest.PageSize

	// 预防某些接口不带 num 和 size，如；ID主站搜索
	if pageNum == 0 {
		pageNum = 1
	}
	if pageSize == 0 {
		pageSize = 20
	}

	begin := int((pageNum - 1) * pageSize)
	end := begin + int(pageSize)
	resultSize := len(stores)
	if begin >= resultSize {
		return nil
	}
	if end >= resultSize {
		end = resultSize
	}
	return stores[begin:end]
}

func (debugInfo *CombineDebugInfo) handlePrepaidCutPage(traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	if len(prepaids) == 0 {
		return prepaids
	}

	// 分页
	pageNum := traceInfo.TraceRequest.PageNum
	pageSize := traceInfo.TraceRequest.PageSize

	// 预防某些接口不带 num 和 size，如；ID主站搜索
	if pageNum == 0 {
		pageNum = 1
	}
	if pageSize == 0 {
		pageSize = 20
	}

	begin := int((pageNum - 1) * pageSize)
	end := begin + int(pageSize)
	resultSize := len(prepaids)
	if begin >= resultSize {
		return nil
	}
	if end >= resultSize {
		end = resultSize
	}
	return prepaids[begin:end]
}

func (debugInfo *CombineDebugInfo) FillPrepaidInfos(traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return
	}
	debugInfo.PrepaidInfos = debugInfo.handlePrepaidCutPage(traceInfo, prepaids)
}

func (debugInfo *CombineDebugInfo) SearchDishCutPage(traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) model.DishInfos {
	if len(dishes) == 0 {
		return dishes
	}
	// 分页
	resultSize := len(dishes)
	begin := int((traceInfo.TraceRequest.PageNum-1)*traceInfo.TraceRequest.PageSize) - int(traceInfo.From*recall.StoreDishRecallSize) // 接口每页20，缓存和ES每页400
	if begin < 0 {
		begin = 0
	}
	if begin >= resultSize {
		return model.DishInfos{}
	}
	end := begin + int(traceInfo.TraceRequest.PageSize)
	if end > resultSize {
		end = resultSize
	}
	return dishes[begin:end]
}

func (debugInfo *CombineDebugInfo) ToString(ctx context.Context, traceInfo *traceinfo.TraceInfo) *string {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return nil
	}

	combinedInfoStr, err := json.Marshal(debugInfo)
	if err == nil {
		return proto.String(string(combinedInfoStr))
	}
	logkit.FromContext(ctx).WithError(err).Error("CombineDebugInfo.ToString failed", logkit.Any("debugInfo", debugInfo))
	return nil
}

func (debugInfo *CombineDebugInfo) FillDishes(traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return
	}
	debugInfo.DishInfos = debugInfo.SearchDishCutPage(traceInfo, dishes)
}

func (debugInfo *CombineDebugInfo) FillDishesAffiliate(traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) {
	if debugInfo == nil || debugInfo.RequestHelperInfo == nil || traceInfo.IsDebug == false {
		return
	}
	debugInfo.DishInfos = dishes
}
