package util

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	uuid "github.com/satori/go.uuid"
)

func GetTokenParseElem(ctx context.Context, token string) (string, uint32) {
	if len(token) < 1 {
		return GetNewSession(), 1
	}
	tokenList := strings.Split(token, "_")
	if len(tokenList) < 2 {
		logkit.FromContext(ctx).Error("token parse error, token", logkit.Any("token", token))
		return GetNewSession(), 1
	}
	pageNum, err := strconv.ParseUint(tokenList[1], 10, 64)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("token ParseUint error, token", logkit.Any("token", token))
	}
	if pageNum < 1 {
		pageNum = 1
	}
	return tokenList[0], uint32(pageNum)
}

func GetToken(sessionId string, pageNum uint32) string {
	return fmt.Sprintf("%v_%v", sessionId, pageNum)
}

func GetNewSession() string {
	return uuid.Must(uuid.NewV4(), nil).String()
}

func BuildStrFromArr(arr []string) string {
	var str string
	for _, s := range arr {
		str += s + " "
	}
	return strings.Trim(str, " ")
}
