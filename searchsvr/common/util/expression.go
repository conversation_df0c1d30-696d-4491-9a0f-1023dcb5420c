package util

import (
	"context"
	"errors"
	"math"
	"strconv"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/Knetic/govaluate"
)

var expFuncMap = map[string]govaluate.ExpressionFunction{
	"expN": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Pow(arguments[0].(float64), arguments[1].(float64))
		return rst, nil
	},
	"log10": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Log10(arguments[0].(float64))
		return rst, nil
	},
	"log2": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Log2(arguments[0].(float64))
		return rst, nil
	},
	"log": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Log(arguments[0].(float64))
		return rst, nil
	},
	"max": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Max(arguments[0].(float64), arguments[1].(float64))
		return rst, nil
	},
	"min": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Min(arguments[0].(float64), arguments[1].(float64))
		return rst, nil
	},
}

// BuildExpression expFuncMap 全局变量，无需指定
func BuildExpression(ctx context.Context, expString string) *govaluate.EvaluableExpression {
	var expression *govaluate.EvaluableExpression
	var err error
	expression, err = govaluate.NewEvaluableExpressionWithFunctions(expString, expFuncMap)
	if err != nil {
		logkit.FromContext(ctx).Error("get expression failed", logkit.String("expString", expString), logkit.Any("expFunc", expFuncMap))
		return nil
	}
	return expression
}

func BuildExpParameters(expParameters string) map[string]interface{} {
	parameters := make(map[string]interface{})
	if len(expParameters) == 0 {
		return parameters
	}
	for _, pair := range strings.Split(expParameters, ",") {
		keyValue := strings.Split(pair, "=")
		if len(keyValue) < 2 {
			continue
		}
		key := keyValue[0]
		value, err := strconv.ParseFloat(keyValue[1], 64)
		if err != nil {
			continue
		}
		parameters[key] = value
	}
	return parameters
}

func FillingExpParameters(original, extra map[string]interface{}) map[string]interface{} {
	if original == nil {
		original = make(map[string]interface{})
	}
	for k, v := range extra {
		original[k] = v
	}
	return original
}

func EvaluateScore(ctx context.Context, expression *govaluate.EvaluableExpression, parameters map[string]interface{}) (float64, error) {
	if expression == nil {
		logkit.FromContext(ctx).Error("EvaluateScore failed to evaluate expression, expression is nil", logkit.Any("expression", expression), logkit.Any("parameters", parameters))
		return 0.0, errors.New("expression is nil")
	}
	val, err := expression.Evaluate(parameters)
	if err != nil || val == nil {
		logkit.FromContext(ctx).WithError(err).Error("EvaluateScore failed to evaluate expression", logkit.Any("expression", expression), logkit.Any("parameters", parameters))
		return 0.0, errors.New("EvaluateScore failed to evaluate expression")
	}
	score := val.(float64)
	if math.IsNaN(score) {
		logkit.FromContext(ctx).WithError(err).Error("EvaluateScore failed to evaluate expression, score is NaN", logkit.Any("expression", expression), logkit.Any("parameters", parameters))
		return 0.0, errors.New("EvaluateScore score is NaN, failed to evaluate expression")
	}
	return score, nil
}
