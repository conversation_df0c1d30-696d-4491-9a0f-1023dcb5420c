package util

import "git.garena.com/shopee/o2o-intelligence/common/common-lib/geohash"

const MaxDistance = 99999.0 //m

func CountDistance(lonX, latX, lonY, latY float64) float64 {
	distance := MaxDistance // m
	if IsZeroFloat64(lonX) && IsZeroFloat64(latX) {
		return distance
	}
	if IsZeroFloat64(lonY) && IsZeroFloat64(latY) {
		return distance
	}
	distance = geohash.DistanceSimplify(
		/* lonX= */ lonX,
		/* latX= */ latX,
		/* lonY= */ lonY,
		/* latY= */ latY)
	return distance
}
