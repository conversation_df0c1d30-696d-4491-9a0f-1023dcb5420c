package rank

import (
	"context"
	"encoding/json"
	"fmt"
	o2oalgo2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/proto/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"math"
	"sort"
	"strconv"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_rank_factor"
	"go.uber.org/zap"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func buildItemFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, nStores int) ([][]byte, []uint64) {
	itemIDs := make([]uint64, nStores, nStores)
	itemFeatures := make([][]byte, nStores, nStores)

	var defaultStoreRankFactor store_rank_factor.StoreRankFactor
	if env.GetCID() != cid.VN {
		defaultStoreRankFactorInterface := store_rank_factor.DefaultStoreRankFactorLoadPlugin.GetData(0)
		if defaultStoreRankFactorInterface != nil {
			if factor, ok := defaultStoreRankFactorInterface.(store_rank_factor.StoreRankFactor); ok {
				defaultStoreRankFactor = factor
			} else {
				logkit.Error("Default store rank factor change fail...")
			}
		}
	}

	// 赋值store.CateRelevance 和 CateRelevanceNorm
	countStoreCateRelNorm(ctx, traceInfo, stores)
	query := strings.ToLower(traceInfo.QueryKeyword)
	queryAscii := strings.ToLower(util.ToAscii(traceInfo.QueryKeyword))
	for i := 0; i < nStores; i++ {
		itemIDs[i] = stores[i].StoreId
		// 旧接口依旧用 DishInfos，新接口则用 DishInfosForFeature
		if traceInfo.IsDishListing == false {
			stores[i].DishInfosForFeature = stores[i].DishInfos
		}

		// 组建菜品特征
		cDishIds, dishPriceList, dishPictureList := buildDishFeatures(stores[i].DishInfosForFeature)
		itemQuerySet := make([]string, 0)
		itemQueryMap := make(map[string]bool)
		for _, itemQuery := range stores[i].RecallQueries {
			if _, ok := itemQueryMap[itemQuery]; !ok {
				itemQueryMap[itemQuery] = true
				itemQuerySet = append(itemQuerySet, itemQuery)
			}
		}
		stores[i].OpeningScore = 0.25
		if stores[i].DisplayOpeningStatus == o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN {
			stores[i].OpeningScore = 0.75
		}

		stores[i].OpeningRatio = stores[i].GetOpeningRatio(query, queryAscii)
		if stores[i].OpeningRatio == 0 {
			stores[i].OpeningRatio = 1
		}
		// 未使用到的特征可以多赋值，对模型计算无影响
		itemFeature := &food.ItemFeature{
			// 4地区共有特征
			CRecallType:         stores[i].RecallTypes,
			CStoreDistanceScore: float32(stores[i].DistanceScore),
			CStoreDistance:      float32(stores[i].Distance),
			CDishId:             cDishIds,
			CEsRelevanceScore:   float32(stores[i].ESScore),
			CIStoreType:         model.StoreTypeNormal,
			StoreState:          int64(stores[i].DisplayOpeningStatus),
		}
		if traceInfo.IsDishListing && stores[i].DishInfos != nil {
			// dish召回特征
			dishRecallTypesDishListing := make([]string, 0, len(stores[i].DishInfos))
			dishScoresDishListing := make([]float32, 0, len(stores[i].DishInfos))
			for _, dish := range stores[i].DishInfos {
				if dish == nil {
					continue
				}
				dishScoresDishListing = append(dishScoresDishListing, float32(dish.Score))
				if len(dish.DishRecallTypes) > 0 {
					dishRecallTypesDishListing = append(dishRecallTypesDishListing, strings.Join(dish.DishRecallTypes, "|"))
				}
			}
			cDishIdsDishListing, dishPricesListing, dishPicturesDishListing := buildDishFeatures(stores[i].DishInfos)
			itemFeature.CNewDishIds = cDishIdsDishListing
			itemFeature.CNewDishScores = dishScoresDishListing
			itemFeature.CNewDishRecallTypes = dishRecallTypesDishListing
			itemFeature.CNewDishPrices = dishPricesListing
			itemFeature.CNewItemDishHasPics = dishPicturesDishListing

			if traceInfo.IsDebug {
				itemFeatureJson, _ := json.Marshal(itemFeature)
				logkit.FromContext(ctx).Info("buildItemFeature", logkit.String("itemFeature", string(itemFeatureJson)))
			}
		}
		// diff mock score处理
		if decision.IsMockModelScore(ctx, traceInfo) {
			// es分数差一位都会巨大的不同，只要小数点后一位
			formattedScore, _ := strconv.ParseFloat(fmt.Sprintf("%.1f", stores[i].ESScore), 64)
			itemFeature.CEsRelevanceScore = float32(formattedScore)
		}

		// relevance 模型新增
		itemFeature.RelevanceIStoreName = strings.ToLower(strings.TrimSpace(stores[i].StoreName))
		dishIdList, dishNameList := buildRelDishFeatures(stores[i].DishInfosForFeature)
		itemFeature.RelevanceIDishIdList = dishIdList
		itemFeature.RelevanceIDishNameList = dishNameList
		itemFeature.RelevanceIStoreCategory = buildStoreCategory(stores[i].MainCategory, stores[i].SubCategory)

		if env.GetCID() == cid.VN {
			// vn 独有特征
			itemFeature.StoreStatusScore = float32(stores[i].OpeningScore)  // vn 独有. 门店状态分数，opening:0.25, 非opening:0.75
			itemFeature.StoreOpeningScore = float32(stores[i].OpeningRatio) // vn 独有. 门店状态因子
			itemFeature.Segment = stores[i].StoreSegment
			itemFeature.BoostFactor = float32(stores[i].StoreSegmentBoostFactor)
		} else {
			// ID,TH,MY 独有特征
			itemFeature.CRankPosition = 0
			itemFeature.CStoreSalesScore = float32(stores[i].StoreSellScore)
			itemFeature.CStoreRatingScore = float32(stores[i].RatingScoreNorm)
			itemFeature.CItemDishPrice = dishPriceList
			itemFeature.CItemDishHasPic = dishPictureList
			itemFeature.CStoreSalesNum = int64(stores[i].StoreSellWeek)
			itemFeature.CStoreAvailableDishCnt = stores[i].StoreAvailableDishCnt
			itemFeature.CStoreDishCnt = stores[i].StoreDishCnt
			itemFeature.CRewriteQuery = itemQuerySet
			if env.GetCID() != cid.VN {
				storeRankFactorInterface := store_rank_factor.StoreRankFactorLoadPlugin.GetData(stores[i].StoreId)
				var storeRankFactor *o2oalgo2.StoreRankFactor = nil
				if storeRankFactorInterface != nil {
					if factor, ok := storeRankFactorInterface.(*o2oalgo2.StoreRankFactor); ok {
						storeRankFactor = factor
					} else {
						logkit.Error("store rank factor change fail...", zap.Uint64("id", stores[i].StoreId))
					}
				}

				cUeFactor := defaultStoreRankFactor.CUeFactor
				cUeRatioFactor := defaultStoreRankFactor.CUeRatioFactor
				cUeFactorOri := defaultStoreRankFactor.CUeFactorOri
				cUeRatioFactorOri := defaultStoreRankFactor.CUeRatioFactorOri
				cPriceIndex := defaultStoreRankFactor.CPriceIndex
				cCommissionRate := defaultStoreRankFactor.CCommissionRate
				cServiceFee := defaultStoreRankFactor.CServiceFee
				cTaxRate := defaultStoreRankFactor.CTaxRate
				cCtr := 0.0
				cCvr := 0.0
				cSalesScoreCate1 := 0.0
				cAccSalesScoreCate1 := 0.0
				cGmvScoreCate1 := 0.0
				cAccGmvScoreCate1 := 0.0
				cSalesScoreCate2 := 0.0
				cAccSalesScoreCate2 := 0.0
				cGmvScoreCate2 := 0.0
				cAccGmvScoreCate2 := 0.0

				if storeRankFactor != nil {
					cUeFactor = storeRankFactor.CUeFactor
					cUeRatioFactor = storeRankFactor.CUeRatioFactor
					cUeFactorOri = storeRankFactor.CUeFactorOri
					cUeRatioFactorOri = storeRankFactor.CUeRatioFactorOri
					cPriceIndex = storeRankFactor.CPriceIndex
					cCommissionRate = storeRankFactor.CCommissionRate
					cServiceFee = storeRankFactor.CServiceFee
					cTaxRate = storeRankFactor.CTaxRate
					cCtr = storeRankFactor.CCtr
					cCvr = storeRankFactor.CCvr
					cSalesScoreCate1 = storeRankFactor.CSalesScoreCate1
					cAccSalesScoreCate1 = storeRankFactor.CAccSalesScoreCate1
					cGmvScoreCate1 = storeRankFactor.CGmvScoreCate1
					cAccGmvScoreCate1 = storeRankFactor.CAccGmvScoreCate1
					cSalesScoreCate2 = storeRankFactor.CSalesScoreCate2
					cAccSalesScoreCate2 = storeRankFactor.CAccSalesScoreCate2
					cGmvScoreCate2 = storeRankFactor.CGmvScoreCate2
					cAccGmvScoreCate2 = storeRankFactor.CAccGmvScoreCate2
				}

				itemFeature.CRatingScore = float32(stores[i].RatingScore / 5.0)
				itemFeature.CUeFactor = float32(cUeFactor)
				itemFeature.CUeRatioFactor = float32(cUeRatioFactor)
				itemFeature.CUeFactorOri = float32(cUeFactorOri)
				itemFeature.CUeRatioFactorOri = float32(cUeRatioFactorOri)
				itemFeature.CPriceIndex = float32(cPriceIndex)
				itemFeature.CCommissionRate = float32(cCommissionRate)
				itemFeature.CServiceFee = float32(cServiceFee)
				itemFeature.CTaxRate = float32(cTaxRate)
				itemFeature.CCtr = float32(cCtr)
				itemFeature.CCvr = float32(cCvr)
				itemFeature.CSalesScoreCate1 = float32(cSalesScoreCate1)
				itemFeature.CAccSalesScoreCate1 = float32(cAccSalesScoreCate1)
				itemFeature.CGmvScoreCate1 = float32(cGmvScoreCate1)
				itemFeature.CAccGmvScoreCate1 = float32(cAccGmvScoreCate1)
				itemFeature.CSalesScoreCate2 = float32(cSalesScoreCate2)
				itemFeature.CAccSalesScoreCate2 = float32(cAccSalesScoreCate2)
				itemFeature.CGmvScoreCate2 = float32(cGmvScoreCate2)
				itemFeature.CAccGmvScoreCate2 = float32(cAccGmvScoreCate2)
				// listwise 新增
				itemFeature.CICateRel = float32(stores[i].CateRelevanceNorm)
			}
		}
		//logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:ItemFeature", logkit.Any("store id", stores[i].StoreId), logkit.Any("item", itemFeature))
		data, _ := proto.Marshal(itemFeature)
		stores[i].ItemFeature = itemFeature
		itemFeatures[i] = data
	}
	return itemFeatures, itemIDs
}

func buildStoreCategory(mainCategory *model.Category, subCategory []*model.Category) string {
	names := "none category"
	cateNames := make([]string, 0, len(subCategory)+1)
	if mainCategory != nil {
		mainName := mainCategory.Level1Name + " : " + mainCategory.Level2Name
		if len(mainName) > 3 {
			cateNames = append(cateNames, mainName)
		}
	}
	for _, cate := range subCategory {
		if cate == nil {
			continue
		}
		subName := cate.Level1Name + " : " + cate.Level2Name
		if len(subName) > 3 {
			cateNames = append(cateNames, subName)
		}
	}
	sort.Slice(cateNames, func(i, j int) bool {
		// 拼接一级类目和二级类目的字符串进行比较
		return cateNames[i] < cateNames[j]
	})
	if len(cateNames) > 0 {
		names = strings.Join(cateNames, " ### ")
	}
	return names
}

func buildDishFeatures(dishInfosForFeature []*model.DishInfo) ([]string, []int64, []int64) {
	cDishIds := make([]string, 0)
	dishPriceList := make([]int64, 0)
	dishPictureList := make([]int64, 0)

	// 新接口用专用的 dishInfosForFeature
	for _, dish := range dishInfosForFeature {
		if dish == nil {
			continue
		}
		cDishIds = append(cDishIds, strconv.Itoa(int(dish.DishId)))
		dishPriceList = append(dishPriceList, int64(dish.Price))
		if dish.HasPicture == true {
			dishPictureList = append(dishPictureList, 1)
		} else {
			dishPictureList = append(dishPictureList, 0)
		}
	}

	return cDishIds, dishPriceList, dishPictureList
}

func buildRelDishFeatures(dishInfosForFeature []*model.DishInfo) (string, string) {
	dishIdListStr := "default dish id"
	dishNameListStr := "none dish"
	if len(dishInfosForFeature) == 0 {
		return dishIdListStr, dishNameListStr
	}
	// 菜品已有顺序，此处相关性特征单独排序，避免影响原有顺序
	dishes := make([]*model.DishNameNormInfo, 0, len(dishInfosForFeature))
	for _, dish := range dishInfosForFeature {
		dishes = append(dishes, &model.DishNameNormInfo{
			DishId:       dish.DishId,
			DishNameNorm: dish.DishNameNorm,
		})
		if len(dishes) >= 2 {
			break // 相关性菜品仅需要2个
		}
	}
	if len(dishes) == 0 {
		return dishIdListStr, dishNameListStr
	}
	sort.Slice(dishes, func(i, j int) bool {
		return dishes[i].DishNameNorm < dishes[j].DishNameNorm
	})
	dishIdList := make([]string, 0, len(dishes))
	dishNameList := make([]string, 0, len(dishes))
	for _, dish := range dishes {
		dishIdList = append(dishIdList, strconv.FormatUint(dish.DishId, 10))
		dishNameList = append(dishNameList, dish.DishNameNorm)
	}
	dishIdListStr = strings.Join(dishIdList, " ### ")
	dishNameListStr = strings.Join(dishNameList, " ### ")
	return dishIdListStr, dishNameListStr
}

func countStoreCateRelNorm(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	maxCateRel := 0.0
	minCateRel := math.MaxFloat64
	for _, store := range stores {
		isStoreCateExpandRecall, score := store.IsStoreCateExpandRecallWithScore()
		if isStoreCateExpandRecall {
			store.CateRelevance = score
			if score > maxCateRel {
				maxCateRel = score
			}
			if score < minCateRel {
				minCateRel = score
			}
		}
	}
	if traceInfo.IsDebug {
		logkit.FromContext(ctx).Info("countStoreCateRelNorm", logkit.Any("maxCateRel", maxCateRel), logkit.Any("minCateRel", minCateRel))
	}
	for _, store := range stores {
		if store.CateRelevance > 0.0 {
			store.CateRelevanceNorm = (store.CateRelevance - minCateRel) / math.Max(maxCateRel-minCateRel, 0.000001)
		}
	}
}

func buildContextFeatureRel(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]byte, error) {
	var intention string
	var maskFlag []int64
	if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
		intention = "store intent"
		maskFlag = []int64{1, 1, 0, 1, 1, 1}
	} else if len(traceInfo.QPResult.QueryDishIntention) > 0 {
		intention = "dish intent"
		maskFlag = []int64{1, 1, 1, 0, 0, 1}
	} else {
		maskFlag = []int64{1, 1, 0, 1, 1, 1}
		intention = "other intent"
	}
	var queryCate string
	for _, categoryIntention := range traceInfo.QPResult.CategoryIntentions {
		if categoryIntention.GetScore() > 0.5 {
			queryCate += categoryIntention.GetLevel2Name() + ","
		}
	}
	queryCate = strings.Trim(queryCate, ",")
	if len(queryCate) == 0 {
		queryCate = "unknown"
	}
	feature := &food.ContextFeature{
		RelIntention: intention,
		RelQueryCate: queryCate,
		RelMaskFlag:  maskFlag,
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:UserContextFeatureRel", logkit.Any("feature", feature))
	return proto.Marshal(feature)
}

func buildItemFeatureRel(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, nStores int) ([][]byte, []uint64) {
	itemIDs := make([]uint64, 0, nStores)
	itemFeatures := make([][]byte, 0, nStores)
	for _, store := range stores {
		if store == nil {
			continue
		}
		itemIDs = append(itemIDs, store.StoreId)
		relDishName := ""
		switch len(store.DishInfos) {
		case 2:
			relDishName = store.DishInfos[0].DishName + "," + store.DishInfos[1].DishName
		case 1:
			relDishName = store.DishInfos[0].DishName
		default:
			relDishName = "none"
		}
		var storeTag string // main category & subcategory 中l2 category_name用逗号拼接
		if store.MainCategory != nil && len(store.MainCategory.Level2Name) > 0 {
			storeTag = store.MainCategory.Level2Name
		}
		for _, subCate := range store.SubCategory {
			if subCate != nil && len(subCate.Level2Name) > 0 {
				storeTag = storeTag + "," + subCate.Level2Name
			}
		}
		if len(storeTag) == 0 {
			storeTag = "unknown"
		}
		itemQuerySet := make([]string, 0)
		itemQueryMap := make(map[string]bool)
		for _, itemQuery := range store.RecallQueries {
			if _, ok := itemQueryMap[itemQuery]; !ok {
				itemQueryMap[itemQuery] = true
				itemQuerySet = append(itemQuerySet, itemQuery)
			}
		}
		if len(itemQuerySet) > 1 {
			//如果该item同时被query和改写query召回，则默认为query去匹配rel得分
			if _, ok := itemQueryMap[traceInfo.QueryKeyword]; ok {
				itemQuerySet = []string{traceInfo.QueryKeyword}
			}
			//特例：如果该item同时新分类和改写query召回，则默认为改写query去匹配rel得分
			for _, recallType := range store.RecallTypes {
				if recallType == foodalgo_search.RecallType_StoreCateExpand.String() {
					for itemQuery := range itemQueryMap {
						if itemQuery != traceInfo.QueryKeyword {
							itemQuerySet = []string{itemQuery}
							break
						}
					}
				}
			}
		}
		itemFeature := &food.ItemFeature{
			RelStoreName:  strings.ToLower(store.StoreName),
			RelStoreCate:  storeTag,
			RelDish:       strings.ToLower(relDishName),
			CRewriteQuery: itemQuerySet,
		}
		//logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:ItemFeatureRel", logkit.Any("store id", store.StoreId), logkit.Any("item", itemFeature))
		data, _ := proto.Marshal(itemFeature)
		itemFeatures = append(itemFeatures, data)
	}
	return itemFeatures, itemIDs
}

func buildItemFeatureForLtr(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, nStores int) ([][]byte, []uint64) {
	itemIDs := make([]uint64, nStores, nStores)
	itemFeatures := make([][]byte, nStores, nStores)
	for i := 0; i < nStores; i++ {
		itemIDs[i] = stores[i].StoreId
		data, _ := proto.Marshal(stores[i].ItemFeature)
		itemFeatures[i] = data
		if traceInfo.IsDebug && i < 20 {
			logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:buildItemFeatureForLtr", logkit.Any("store id", stores[i].StoreId), logkit.Any("item", stores[i].ItemFeature))
		}
	}
	return itemFeatures, itemIDs
}
