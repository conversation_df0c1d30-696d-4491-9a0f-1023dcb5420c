package rank

import (
	"context"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func buildPrepaidContextFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo) (*food.ContextFeature, []byte) {
	feature := &food.ContextFeature{
		UserId:         int64(traceInfo.UserId),
		Query:          traceInfo.QueryKeyword,
		CQueryCategory: buildQueryCategoryFea(traceInfo.QPResult.ProbFilterCategoryIntentions),
		LogId:          traceInfo.TraceRequest.PublishId,

		CQueryDishIntention:  util.ConvertStringToBoolInt64(traceInfo.QPResult.QueryDishIntention),
		CQuerySegments:       traceInfo.QPResult.Segments,
		CQueryStoreIntention: util.ConvertStringToBoolInt64(traceInfo.QPResult.QueryStoreIntention),
		Longitude:            traceInfo.UserContext.Longitude,
		Latitude:             traceInfo.UserContext.Latitude,
		CSearchTimestamp:     time.Now().Unix(),
	}
	if traceInfo.IsDebug {
		logkit.FromContext(ctx).Info("BuildPrepaidContextFeature", logkit.Any("feature", feature))
	}
	data, _ := proto.Marshal(feature)
	return feature, data
}

func buildQueryCategoryFea(intentions []*qp.CategoryIntention) string {
	names := "none category"
	if len(intentions) == 0 {
		return names
	}
	cateNames := make([]string, 0, len(intentions))
	for _, cate := range intentions {
		if cate == nil {
			continue
		}
		name := cate.GetLevel1Name() + " : " + cate.GetLevel2Name()
		if len(name) > 3 {
			cateNames = append(cateNames, name)
		}
	}
	sort.Slice(cateNames, func(i, j int) bool {
		// 拼接一级类目和二级类目的字符串进行比较
		return cateNames[i] < cateNames[j]
	})
	if len(cateNames) > 0 {
		names = strings.Join(cateNames, " ### ")
	}
	return names
}

// PrepaidIds + PrepaidFeature
func buildPrepaidFeatures(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) ([]uint64, [][]byte) {
	prepaidLen := len(prepaids)
	prepaidIds := make([]uint64, prepaidLen)
	prepaidFeatures := make([][]byte, prepaidLen)
	for i, prepaid := range prepaids {
		if prepaid == nil || prepaid.StoreMeta == nil || prepaid.DishMeta == nil {
			continue
		}
		prepaidIds[i] = prepaid.PrepaidId
		prepaidFea := buildPrepaidFeature(ctx, traceInfo, prepaid, i)
		prepaidFeatures[i] = prepaidFea
	}
	return prepaidIds, prepaidFeatures
}

// StoreIds & PrepaidFeature
func buildSQSItemFeatures(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) ([]uint64, [][]byte) {
	prepaidLen := len(prepaids)
	storeIds := make([]uint64, prepaidLen)
	itemFeatures := make([][]byte, prepaidLen)
	for i, prepaid := range prepaids {
		if prepaid == nil || prepaid.StoreMeta == nil || prepaid.DishMeta == nil {
			continue
		}
		storeIds[i] = prepaid.StoreId
		itemFea := buildSQSItemFeature(ctx, traceInfo, prepaid, i)
		itemFeatures[i] = itemFea
	}
	return storeIds, itemFeatures
}

// ItemFeature
func buildSQSItemFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaid *model.PrepaidInfo, i int) []byte {
	itemFeature := &food.ItemFeature{}
	// relevance 模型新增
	itemFeature.RelevanceIStoreName = strings.ToLower(strings.TrimSpace(prepaid.StoreMeta.GetName()))
	dishIdList, dishNameList := buildPrepaidRelDishFeatures(prepaid.DishMeta)
	itemFeature.RelevanceIDishIdList = dishIdList
	itemFeature.RelevanceIDishNameList = dishNameList
	itemFeature.RelevanceIStoreCategory = buildStoreCategory(model.GetMainCategory(ctx, prepaid.StoreId, prepaid.StoreMeta.GetMainCategory()), model.GetSubCategory(ctx, prepaid.StoreId, prepaid.StoreMeta.GetSubCategory()))
	if traceInfo.IsDebug && i < 10 {
		logkit.FromContext(ctx).Info("buildSQSItemFeature", logkit.Uint64("prepaid id", prepaid.PrepaidId), logkit.Any("item", itemFeature))
	}
	data, _ := proto.Marshal(itemFeature)
	return data
}

// PrepaidFeature
func buildPrepaidFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaid *model.PrepaidInfo, i int) []byte {
	feature := &food.PrepaidFeature{}
	feature.PrepaidId = int64(prepaid.PrepaidId)
	feature.CPrepaidSkuStoreId = int64(prepaid.StoreId)
	feature.CPrepaidSkuDishId = int64(prepaid.DishId)
	feature.CPrepaidSkuDistance = float32(prepaid.Distance)
	feature.CPrepaidOriginPrice = float32(prepaid.DishMeta.GetPrice())
	feature.CPrepaidPrice = float32(prepaid.PrepaidMeta.GetListPrice())
	feature.CPrepaidDiscount = float32(prepaid.PrepaidDiscount)
	feature.CPrepaidSaleNum = float32(prepaid.PrepaidMeta.GetSalesVolume())
	feature.CPrepaidEsScore = float32(prepaid.ESScore)
	feature.CPrepaidDistScore = float32(prepaid.GetFeatureParamsFloat(ctx, "dist_score"))
	feature.CPrepaidSkuCtr = float32(prepaid.GetFeatureParamsFloat(ctx, "prepaid_sku_ctr"))
	feature.CPrepaidSkuCvr = float32(prepaid.GetFeatureParamsFloat(ctx, "prepaid_sku_cvr"))
	feature.CQueryPrepaidSkuCtr = float32(prepaid.GetFeatureParamsFloat(ctx, "query_prepaid_sku_ctr"))
	feature.CQueryPrepaidSkuCvr = float32(prepaid.GetFeatureParamsFloat(ctx, "query_prepaid_sku_cvr"))
	feature.CPrepaidDiscountScore = float32(prepaid.GetFeatureParamsFloat(ctx, "discount_score"))
	feature.CPrepaidSaleScore = float32(prepaid.GetFeatureParamsFloat(ctx, "sale_score"))
	feature.CPrepaidIDishCtr = float32(prepaid.GetFeatureParamsFloat(ctx, "i_dish_ctr"))
	feature.CPrepaidIDishCvr = float32(prepaid.GetFeatureParamsFloat(ctx, "i_dish_cvr"))
	feature.CPrepaidUserBrandPurchase = float32(prepaid.GetFeatureParamsFloat(ctx, "user_brand_purchase"))
	if traceInfo.IsDebug && i < 10 {
		logkit.FromContext(ctx).Info("buildSQSPrepaidFeature", logkit.Uint64("prepaid id", prepaid.PrepaidId), logkit.Any("feature", feature))
	}
	data, _ := proto.Marshal(feature)
	return data
}

func buildPrepaidRelDishFeatures(dishMeta *o2oalgo.Dish) (string, string) {
	dishIdListStr := "default dish id"
	dishNameListStr := "none dish"
	if dishMeta == nil || len(dishMeta.GetName()) == 0 {
		return dishIdListStr, dishNameListStr
	}
	dishIdListStr = strconv.FormatUint(dishMeta.GetId(), 10)
	dishNameListStr = strings.TrimSpace(strings.ToLower(dishMeta.GetName()))
	return dishIdListStr, dishNameListStr
}
