package rank

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

func PrepaidRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhrasePrepaidRank, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenRank, len(prepaids))
	}()
	if len(prepaids) == 0 {
		return prepaids
	}
	// 融合因子补充，模型因子在此填充，剩余其他大部分打分因子在filling阶段填充,直接查看FeatureParams 字段赋值
	prepaids = FusionModelPredict(ctx, traceInfo, prepaids)

	// 融合公式计算 fusion score
	prepaids = batchCalFusionScore(ctx, traceInfo, prepaids)

	// 排序
	prepaids.SortByFusionScore()
	return prepaids
}

// 精排模型，填充模型因子
func FusionModelPredict(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	if len(prepaids) == 0 {
		logkit.FromContext(ctx).Error("PrepaidInfos is empty, skip model prediction")
		return prepaids
	}
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchPrepaidFewResult {
		return prepaids
	}
	// 获取精排模型因子配置
	prepaidFusionCfgs := abtest.GetPrepaidFusionModelConfigs(ctx, traceInfo.AbParamClient, traceInfo.IsDebug)
	if len(prepaidFusionCfgs) == 0 {
		logkit.FromContext(ctx).Error("PrepaidFusionModelConfigs is empty, skip model prediction")
		return prepaids
	}
	traceInfo.ModelFactorConfigs = prepaidFusionCfgs
	// 填充context feature, item feature, prepaid feature
	_, ctxFeaBytes := buildPrepaidContextFeature(ctx, traceInfo)
	storeIds, itemFeasBytes := buildSQSItemFeatures(ctx, traceInfo, prepaids)
	prepaidIds, prepaidFeasBytes := buildPrepaidFeatures(ctx, traceInfo, prepaids)
	// 调用模型预估，获取对应分数
	prepaidScores := PredictMultiModels(ctx, traceInfo, prepaidFusionCfgs, ctxFeaBytes, storeIds, itemFeasBytes, prepaidIds, prepaidFeasBytes)
	// 分数填充
	for _, prepaid := range prepaids {
		if prepaid == nil {
			continue
		}
		if prepaidScores[prepaid.PrepaidId] == nil {
			logkit.FromContext(ctx).Warn("Prepaid score is nil for store", zap.Uint64("PrepaidId", prepaid.PrepaidId))
			continue
		}
		for factorKey, score := range prepaidScores[prepaid.PrepaidId] {
			prepaid.FeatureParams[factorKey] = score
		}
	}
	return prepaids
}

func batchCalFusionScore(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	// 配置获取 exp & weight param
	prepaidFusionCfg := abtest.GetPrepaidFusionRankConfig(ctx, traceInfo.AbParamClient)
	if prepaidFusionCfg == nil || len(prepaidFusionCfg.ExpString) == 0 {
		logkit.FromContext(ctx).Error("PrepaidFusionRankConfig is nil or ExpString is empty, skip fusion score calculation")
		return prepaids
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "PrepaidFusionRankConfig", zap.String("PipelineType", traceInfo.PipelineType.String()), zap.String("ExpString", prepaidFusionCfg.ExpString), zap.String("ExpParameters", prepaidFusionCfg.ExpParameters))
	expression := util.BuildExpression(ctx, prepaidFusionCfg.ExpString)
	// 公共权重参数填充，w1~w7
	parameters := util.BuildExpParameters(prepaidFusionCfg.ExpParameters)
	for _, prepaid := range prepaids {
		// 每个prepaid的权重参数填充
		prepaid.FeatureParams = util.FillingExpParameters(prepaid.FeatureParams, parameters)
		// 计算融合分数
		prepaid.FusionScore, _ = util.EvaluateScore(ctx, expression, prepaid.FeatureParams)
	}
	return prepaids
}
