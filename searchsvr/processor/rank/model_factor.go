package rank

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

// 多目标模型
func PredictMultiModels(ctx context.Context, traceInfo *traceinfo.TraceInfo, modelConfigs []*config.ModelFactorConfig, ctxFeature []byte, storeIds []uint64, itemFeatures [][]byte, prepaidIds []uint64, prepaidFeatures [][]byte) map[uint64]map[string]float64 {
	result := make(map[uint64]map[string]float64, len(prepaidIds))
	if len(modelConfigs) == 0 {
		return result
	}
	batchRes := make([]map[uint64]map[string]float64, len(modelConfigs))
	wg := sync.WaitGroup{}
	for i, modelConfig := range modelConfigs {
		if modelConfig == nil {
			continue
		}
		wg.Add(1)
		goroutine.WithGo(ctx, "PredictModels", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			configTmp := modelConfigs[index]
			temRes := PredictModels(ctx, traceInfo, *configTmp, ctxFeature, storeIds, itemFeatures, prepaidIds, prepaidFeatures)
			batchRes[index] = temRes
		}, i)
	}
	wg.Wait()

	for _, tempRes := range batchRes {
		for itemId, factorScores := range tempRes {
			if result[itemId] == nil {
				result[itemId] = make(map[string]float64, len(factorScores))
			}
			for factorKey, score := range factorScores {
				result[itemId][factorKey] = score
			}
		}
	}
	return result
}

func PredictModels(ctx context.Context, traceInfo *traceinfo.TraceInfo, modelConfig config.ModelFactorConfig, ctxFeature []byte, storeIds []uint64, itemFeatures [][]byte, prepaidIds []uint64, prepaidFeatures [][]byte) map[uint64]map[string]float64 {
	result := make(map[uint64]map[string]float64, len(prepaidIds))
	if len(modelConfig.ModelName) == 0 {
		logkit.FromContext(ctx).Error("PredictModels modelConfig.ModelName is empty, skip model prediction", logkit.Any("modelConfig", modelConfig))
		return result
	}
	// 默认值赋值
	for _, factorConfig := range modelConfig.FactorKeyConfigs {
		for _, itemId := range prepaidIds {
			if result[itemId] == nil {
				result[itemId] = make(map[string]float64, len(modelConfig.FactorKeyConfigs))
			}
			result[itemId][factorConfig.FactorKey] = factorConfig.DefaultScore
		}
	}
	reqModelName := modelConfig.ModelName
	if modelConfig.IsRoughPredict {
		reqModelName = modelConfig.ModelName + "@rough_predict"
	}
	// 进行模型预估调用
	predictReq := &predictor.PredictReq{
		Uid:      traceInfo.UserId,
		Itemids:  prepaidIds,
		CtxFea:   ctxFeature,
		Reqid:    traceInfo.TraceRequest.PublishId,
		Models:   []string{reqModelName},
		ItemFeas: prepaidFeatures,
	}
	for _, factorConf := range modelConfig.FactorKeyConfigs {
		if factorConf != nil && factorConf.ItemFeasType == config.ItemFeasTypeItemFeature {
			predictReq.Itemids = storeIds
			predictReq.ItemFeas = itemFeatures
			break
		}
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseFusionRankPredict)+"_"+modelConfig.ModelName), time.Since(pt))
	}()
	resp, err := mlplatform.PredictV2(ctx, predictReq, modelConfig.ModelName, traceInfo.IsDebug)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.RANK_ERROR)
		logkit.FromContext(ctx).Error("failed to predict model", logkit.Any("err", err), logkit.String("modelName", modelConfig.ModelName))
		return result
	}
	if modelConfig.IsACKInstance {
		traceInfo.PredictConfig.AckIpInstance = resp.GetIpaddr()
		traceInfo.PredictConfig.CtrModuleName = modelConfig.ModelName
	}
	// 处理模型返回的分数
	for _, factorConfig := range modelConfig.FactorKeyConfigs {
		modScores := resp.GetModScoresInfo()[factorConfig.ModelName]
		if len(modScores.GetScores()) == 0 {
			logkit.FromContext(ctx).Error("failed to get model score, score list empty", logkit.String("modelName", modelConfig.ModelName), logkit.Any("modScores", modScores))
			continue
		}
		scoreList := modScores.GetScores()[factorConfig.ScoreKey]
		if scoreList == nil {
			logkit.FromContext(ctx).Error("failed to get rel score, score list empty", logkit.String("modelName", modelConfig.ModelName), logkit.Any("modScores", modScores))
			continue
		}
		scores := scoreList.GetScores()
		if len(scores) != len(prepaidIds) {
			logkit.FromContext(ctx).Error("failed to get rel score, scores length diff", logkit.String("modelName", modelConfig.ModelName), logkit.Int("scoresLen", len(scores)), logkit.Int("itemLen", len(prepaidIds)))
			continue
		}
		for i, itemId := range prepaidIds {
			score := scores[i]
			if score == 0.0 {
				continue
			}
			if factorConfig.MinScore != 0.0 && score < factorConfig.MinScore {
				score = factorConfig.MinScore
			}
			if factorConfig.MaxScore != 0.0 && score > factorConfig.MaxScore {
				score = factorConfig.MaxScore
			}
			result[itemId][factorConfig.FactorKey] = score
		}
	}
	return result
}

func PrepaidPredictAckDump(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) {
	if len(prepaids) == 0 || len(traceInfo.PredictConfig.AckIpInstance) == 0 || len(traceInfo.PredictConfig.CtrModuleName) == 0 {
		logkit.FromContext(ctx).Error("PredictAckDump: AckIpInstance or CtrModuleName is empty, skip ack predict",
			logkit.String("AckIpInstance", traceInfo.PredictConfig.AckIpInstance),
			logkit.String("CtrModuleName", traceInfo.PredictConfig.CtrModuleName),
			logkit.Int("prepaidLen", len(prepaids)),
			logkit.Any("ModelFactorConfigs", traceInfo.ModelFactorConfigs))
		return
	}
	var res model.PrepaidInfos
	ackNum := 150
	if apollo.SearchApolloCfg.PredictAckNum != 0 {
		ackNum = apollo.SearchApolloCfg.PredictAckNum
	}
	if len(prepaids) > ackNum {
		res = prepaids[:ackNum]
	} else {
		res = prepaids
	}
	itemLen := len(res)
	itemIds := make([]uint64, itemLen)
	fusionScores := make([]float64, itemLen)
	modScoresInfoMap := make(map[string]*predictor.ScoresInfo)
	fusionScoreMap := make(map[string]*predictor.ScoreL)
	for i, prepaid := range res {
		itemIds[i] = prepaid.PrepaidId
		fusionScores[i] = prepaid.FusionScore
	}
	fusionScoreMap["fusion_scores"] = &predictor.ScoreL{Scores: fusionScores}
	// 自动填充模型因子
	buildModScoresInfo(ctx, traceInfo, traceInfo.ModelFactorConfigs, res, modScoresInfoMap)

	abTestGroup := strings.Split(traceInfo.ABTestGroup.GetABTestString(), ",")
	req := &predictor.PreAckReq{
		Reqid:           traceInfo.TraceRequest.PublishId,
		Itemids:         itemIds,
		AbGroup:         abTestGroup,
		ModFusionScores: fusionScoreMap,
		ModScoresInfo:   modScoresInfoMap,
		ModelName:       traceInfo.PredictConfig.CtrModuleName,
	}
	startTime := time.Now()
	var resp *predictor.PreAckRsp
	var err error
	resp, err = mlplatform.PredictAck(ctx, req, traceInfo.PredictConfig.AckIpInstance, traceInfo.PredictConfig.AckIpInstance, traceInfo.IsDebug)
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(req)
		respStr, _ := json.Marshal(resp)
		logkit.FromContext(ctx).Info("prepaid predict debug log:ack",
			zap.String("req", string(reqStr)),
			zap.String("resp", string(respStr)),
			zap.String("AckIpInstance", traceInfo.PredictConfig.AckIpInstance))
	}
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "prepaid_predict_ack")
	if err != nil {
		logkit.FromContext(ctx).Error("prepaid predict debug log:failed to predict ctr ack", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("publish_id", traceInfo.TraceRequest.PublishId))
		metric_reporter.ReportClientRequestError(1, "prepaid_predict_ack", "failed")
		return
	}
	metric_reporter.ReportClientRequestError(1, "prepaid_predict_ack", "0")
	if resp == nil || resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("prepaid predict debug log:failed to predict ack", logkit.Any("CtrModuleName", traceInfo.PredictConfig.CtrModuleName), logkit.Any("publish_id", traceInfo.TraceRequest.PublishId), logkit.Any("resp", resp))
	}
	return
}

func buildModScoresInfo(ctx context.Context, traceInfo *traceinfo.TraceInfo, modelConfigs []*config.ModelFactorConfig, prepaids model.PrepaidInfos, modScoresInfo map[string]*predictor.ScoresInfo) {
	if len(modelConfigs) == 0 {
		return
	}
	for _, modelConfig := range modelConfigs {
		if modelConfig == nil || len(modelConfig.FactorKeyConfigs) == 0 {
			logkit.FromContext(ctx).Error("buildModScoresInfo modelConfig is nil or FactorKeyConfigs is empty", logkit.Any("modelConfig", modelConfig))
			continue
		}
		for _, factorConfig := range modelConfig.FactorKeyConfigs {
			if factorConfig == nil || len(factorConfig.ModelName) == 0 {
				logkit.FromContext(ctx).Error("buildModScoresInfo factorConfig is nil or ModelName is empty", logkit.Any("factorConfig", factorConfig))
				continue
			}
			scores := make([]float64, len(prepaids))
			for i, prepaid := range prepaids {
				score, exist := prepaid.FeatureParams[factorConfig.FactorKey]
				if !exist || score == nil {
					continue
				}
				if floatVal, ok := score.(float64); ok {
					scores[i] = floatVal
				} else {
					scores[i] = 0.0
					logkit.FromContext(ctx).Error("buildModScoresInfo Value is not float64",
						logkit.String("key", factorConfig.FactorKey), logkit.Any("score", score), logkit.String("type", fmt.Sprintf("%T", score)))
				}
			}
			if _, ok := modScoresInfo[factorConfig.ModelName]; !ok {
				modScoresInfo[factorConfig.ModelName] = &predictor.ScoresInfo{
					Scores: make(map[string]*predictor.ScoreList),
				}
			}
			modScoresInfo[factorConfig.ModelName].Scores[factorConfig.FactorKey+"_scores"] = &predictor.ScoreList{
				Scores: scores,
			}
		}
	}
}
