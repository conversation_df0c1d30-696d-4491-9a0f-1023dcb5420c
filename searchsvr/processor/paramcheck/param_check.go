package paramcheck

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	maxPageSize = 50
)

func SearchParamCheckInValid(ctx context.Context, req *foodalgo_search.SearchRequest, handlerType traceinfo.HandlerType) (error, bool) {
	switch handlerType {
	case traceinfo.HandlerTypeSearch:
		return searchParamCheck(ctx, req)
	case traceinfo.HandlerTypeSearchFood, traceinfo.HandlerTypeSearchMart:
		return vnSearchParamCheck(ctx, req)
	case traceinfo.HandlerTypeSearchGlobal:
		return vnSearchGlobalParamCheck(ctx, req)
	case traceinfo.HandlerTypeSearchGlobalV1, traceinfo.HandlerTypeSearchIdsStores, traceinfo.HandlerTypeSearchIdsDishes, traceinfo.HandlerTypeSearchIdsWeb, traceinfo.HandlerTypeSearchIdsFoody:
		return vnSearchGlobalV1ParamCheck(ctx, req)
	case traceinfo.HandlerTypeSearchFoodTotalNum, traceinfo.HandlerTypeSearchMartTotalNum:
		return vnTotalNumParamCheck(ctx, req)
	}
	return nil, false
}

func DishListingParamCheck(ctx context.Context, req *foodalgo_search.SearchStoresWithListingDishReq) (error, bool) {
	if len(req.GetKeyword()) == 0 {
		logkit.FromContext(ctx).Error("params keyword invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if req.Longitude == nil || req.Latitude == nil {
		logkit.FromContext(ctx).Error("params longitude or latitude invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug && req.GetPageSize() > maxPageSize {
		// live不允许debug超过50
		if env.GetEnv() == "live" {
			logkit.FromContext(ctx).Error("page size limit", logkit.Uint32("page_size", req.GetPageSize()))
			return errno.ErrLimitSize, true
		}
	}
	return nil, false
}

func searchParamCheck(ctx context.Context, req *foodalgo_search.SearchRequest) (error, bool) {
	if len(req.GetKeyword()) == 0 ||
		req.Longitude == nil || req.Latitude == nil || req.GetPageNum() == 0 {
		logkit.FromContext(ctx).Error("params invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if !req.GetIsDebug() && req.GetPageSize() > maxPageSize {
		logkit.FromContext(ctx).Error("page size limit", logkit.Uint32("page_size", req.GetPageSize()))
		return errno.ErrLimitSize, true
	}
	return nil, false
}

func vnSearchGlobalParamCheck(ctx context.Context, req *foodalgo_search.SearchRequest) (error, bool) {
	if len(req.GetKeyword()) == 0 {
		logkit.FromContext(ctx).Error("params invalid, keyword", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if !req.GetIsDebug() && req.GetPageSize() > 400 {
		logkit.FromContext(ctx).Error("page size limit, not greater than 400 for search global", logkit.Uint32("page_size", req.GetPageSize()))
		return errno.ErrLimitSize, true
	}
	if req.GetDistance() > constants.MaxDeliveryDistance {
		logkit.FromContext(ctx).Error("SearchGlobal distance can not lager than 20km", logkit.Any("req", req))
		return errno.ErrParamsInvalid, true
	}
	if req.GetDistance() > 0 && util.IsZeroFloat32(req.GetLongitude()) && util.IsZeroFloat32(req.GetLatitude()) {
		logkit.FromContext(ctx).Error("params invalid, distance or longitude or latitude is invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	switch req.GetSortType() {
	case foodalgo_search.SearchRequest_Relevance, foodalgo_search.SearchRequest_Nearby, foodalgo_search.SearchRequest_TopSales, foodalgo_search.SearchRequest_BestRated:
		logger.MyDebug(ctx, false, "sort", logkit.Any("sort type", req.GetSortType()))
	default:
		logkit.FromContext(ctx).Error("params sort type invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	// CategoryType 不传，默认按照两种类型处理
	if len(req.GetFilterType().GetCategoryType()) == 0 {
		if req.GetFilterType() == nil {
			req.FilterType = &foodalgo_search.SearchRequest_FilterType{}
		}
		req.FilterType.CategoryType = []uint32{traceinfo.CategoryTypeFood, traceinfo.CategoryTypeMart}
	}
	return nil, false
}

func vnSearchGlobalV1ParamCheck(ctx context.Context, req *foodalgo_search.SearchRequest) (error, bool) {
	if !req.GetIsDebug() && req.GetPageSize() > 400 {
		logkit.FromContext(ctx).Error("page size limit, not greater than 400 for search global", logkit.Uint32("page_size", req.GetPageSize()))
		return errno.ErrLimitSize, true
	}
	if req.GetDistance() > constants.MaxDeliveryDistance {
		logkit.FromContext(ctx).Error("SearchGlobal distance can not lager than 20km", logkit.Any("req", req))
		return errno.ErrParamsInvalid, true
	}
	switch req.GetSortType() {
	case foodalgo_search.SearchRequest_Relevance, foodalgo_search.SearchRequest_Nearby, foodalgo_search.SearchRequest_TopSales, foodalgo_search.SearchRequest_BestRated:
		logger.MyDebug(ctx, false, "sort", logkit.Any("sort type", req.GetSortType()))
	default:
		logkit.FromContext(ctx).Error("params sort type invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	return nil, false
}

func vnSearchParamCheck(ctx context.Context, req *foodalgo_search.SearchRequest) (error, bool) {
	if len(req.GetKeyword()) == 0 || req.Longitude == nil || req.Latitude == nil {
		logkit.FromContext(ctx).Error("SearchGlobalV2 params invalid, keyword or longitude or latitude is invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if !req.GetIsDebug() && (req.GetPageSize() > 400 || req.GetPageSize() <= 0) {
		logkit.FromContext(ctx).Error("SearchGlobalV2 page size limit, not >=400 or <=0 for search global", logkit.Uint32("page_size", req.GetPageSize()))
		return errno.ErrLimitSize, true
	}
	// CategoryType 不传，默认按照两种类型处理
	if len(req.GetFilterType().GetCategoryType()) == 0 {
		if req.GetFilterType() == nil {
			req.FilterType = &foodalgo_search.SearchRequest_FilterType{}
		}
		req.FilterType.CategoryType = []uint32{traceinfo.CategoryTypeFood}
	}
	if req.GetFilterType().GetCategoryType() == nil || len(req.GetFilterType().GetCategoryType()) != 1 {
		logkit.FromContext(ctx).Error("SearchGlobalV2 categoryType can not be empty for search global", logkit.Any("req", req))
		return errno.ErrParamsInvalid, true
	}
	if req.GetDistance() > constants.MaxDeliveryDistance {
		logkit.FromContext(ctx).Error("SearchGlobalV2 distance can not lager than 20km", logkit.Any("req", req))
		return errno.ErrParamsInvalid, true
	}
	switch req.GetSortType() {
	case foodalgo_search.SearchRequest_Relevance, foodalgo_search.SearchRequest_Nearby, foodalgo_search.SearchRequest_TopSales, foodalgo_search.SearchRequest_BestRated:
		logger.MyDebug(ctx, false, "SearchGlobalV2 sort", logkit.Any("sort type", req.GetSortType()))
	default:
		logkit.FromContext(ctx).Error("SearchGlobalV2 params sort type invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	return nil, false
}

func vnTotalNumParamCheck(ctx context.Context, req *foodalgo_search.SearchRequest) (error, bool) {
	if len(req.GetKeyword()) == 0 || req.Longitude == nil || req.Latitude == nil {
		logkit.FromContext(ctx).Error("TotalNum params invalid, keyword or longitude or latitude is invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	// CategoryType 不传，默认按照两种类型处理
	if len(req.GetFilterType().GetCategoryType()) == 0 {
		if req.GetFilterType() == nil {
			req.FilterType = &foodalgo_search.SearchRequest_FilterType{}
		}
		req.FilterType.CategoryType = []uint32{traceinfo.CategoryTypeFood}
	}
	if req.GetFilterType().GetCategoryType() == nil || len(req.GetFilterType().GetCategoryType()) != 1 {
		logkit.FromContext(ctx).Error("TotalNum categoryType can not be empty for search global", logkit.Any("req", req))
		return errno.ErrParamsInvalid, true
	}
	if req.GetDistance() > constants.MaxDeliveryDistance {
		logkit.FromContext(ctx).Error("TotalNum distance can not lager than 20km", logkit.Any("req", req))
		return errno.ErrParamsInvalid, true
	}
	switch req.GetSortType() {
	case foodalgo_search.SearchRequest_Relevance, foodalgo_search.SearchRequest_Nearby, foodalgo_search.SearchRequest_TopSales, foodalgo_search.SearchRequest_BestRated:
		logger.MyDebug(ctx, false, "TotalNum sort", logkit.Any("sort type", req.GetSortType()))
	default:
		logkit.FromContext(ctx).Error("TotalNum params sort type invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	return nil, false
}

func DishRecallParamCheck(ctx context.Context, req *foodalgo_search.DishRecallReq) (error, bool) {
	if len(req.GetKeyword()) == 0 {
		logkit.FromContext(ctx).Error("params keyword invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if req.Longitude == nil || req.Latitude == nil {
		logkit.FromContext(ctx).Error("params longitude or latitude invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if len(req.GetStoreIds()) == 0 {
		logkit.FromContext(ctx).Error("params storeIds invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	// 默认1000个，防止异常请求
	if len(req.GetStoreIds()) > apollo.GetMaxDishRecallStoreSize() {
		logkit.FromContext(ctx).Error("params storeIds too long", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	return nil, false
}

func SearchPrepaidSKULandingPageParamCheck(ctx context.Context, req *foodalgo_search.SearchPrepaidSKULandingPageReq) (error, bool) {
	if len(req.GetKeyword()) == 0 {
		logkit.FromContext(ctx).Error("params keyword invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if req.Longitude == nil || req.Latitude == nil {
		logkit.FromContext(ctx).Error("params longitude or latitude invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug && req.GetPageSize() > maxPageSize {
		// live不允许debug超过50
		if env.GetEnv() == "live" {
			logkit.FromContext(ctx).Error("page size limit", logkit.Uint32("page_size", req.GetPageSize()))
			return errno.ErrLimitSize, true
		}
	}
	return nil, false
}

func SearchPrepaidSKUForBagParamCheck(ctx context.Context, req *foodalgo_search.SearchPrepaidSKUForBagReq) (error, bool) {
	if len(req.GetKeyword()) == 0 {
		logkit.FromContext(ctx).Error("params keyword invalid", logkit.String("req", req.String()))
		return errno.ErrParamsInvalid, true
	}
	if req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug && req.GetPageSize() > maxPageSize {
		// live不允许debug超过50
		if env.GetEnv() == "live" {
			logkit.FromContext(ctx).Error("page size limit", logkit.Uint32("page_size", req.GetPageSize()))
			return errno.ErrLimitSize, true
		}
	}
	return nil, false
}
