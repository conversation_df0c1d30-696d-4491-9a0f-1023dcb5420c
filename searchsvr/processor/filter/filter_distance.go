package filter

import (
	"context"
	"math"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/polygon"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StorePolygonDistanceFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	// diff 情况下默认都不过滤
	if decision.IsMockModelScore(ctx, traceInfo) {
		return storeInfos
	}

	if len(storeInfos) == 0 {
		return storeInfos
	}
	if traceInfo.IsDowngradeDataServer {
		return storeInfos
	}
	if util.IsZeroFloat64(traceInfo.TraceRequest.Longitude) && util.IsZeroFloat64(traceInfo.TraceRequest.Latitude) {
		return storeInfos
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFilterPolygonDistance, time.Since(pt))
	}()
	resStoreIDs := make([]*model.StoreInfo, 0, len(storeInfos))
	storePolygonFilterTime := time.Now()
	for _, store := range storeInfos {
		// 多边形数据异常
		if isActiveByStoreDistance(traceInfo, store, traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude) || isActiveByStorePickupDistance(traceInfo, store, traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude) {
			resStoreIDs = append(resStoreIDs, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeStoreDistance, store.Distance)
		}
	}
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "storePolygonFilter"), zap.String("cost", time.Since(storePolygonFilterTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(storePolygonFilterTime), metric_reporter2.SearchReportTypeRpc, "", "", "storePolygonFilter")
	return resStoreIDs
}

// 1.正常召回
//
//	1.1 不带self pickup filter，用商家配送距离
//	1.2 带self pickup filter，用自提距离
//
// 2.自提召回
//
//	2.1 全部用自提距离
func isActiveByStoreDistance(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo, locPointLatitude, locPointLongitude float64) bool {
	// 带 self pickup filter 或者仅自提商家召回的，不使用商家配送距离过滤
	if (env.GetCID() == cid.TH || env.GetCID() == cid.MY) && (decision.IsSelfPickupFilter(traceInfo) || store.IsOnlySelfPickupRecall()) {
		return false
	}
	minDeliveryDis := uint64(math.Min(float64(store.DeliveryDistance), float64(constants.MaxDeliveryDistance)))
	// 配置
	if apollo.SearchApolloCfg.PolygonDistanceFilter == false {
		if uint64(math.Floor(store.Distance)) > minDeliveryDis {
			return false
		}
		return true
	}
	// 没有多边形数据或者异常
	if store.StorePolygon == nil {
		if uint64(math.Floor(store.Distance)) > minDeliveryDis {
			return false
		}
		return true
	}
	if !(store.StorePolygon.Contains(locPointLatitude, locPointLongitude)) {
		return false
	}
	// 这个20公里是必须的隐形条件
	if uint64(math.Floor(store.Distance)) > uint64(float64(constants.MaxDeliveryDistance)) {
		return false
	}
	return true
}

func isActiveByStorePickupDistance(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo, locPointLatitude, locPointLongitude float64) bool {
	// 不满足自提商家召回条件，直接跳过
	if !(env.GetCID() == cid.TH || env.GetCID() == cid.MY) {
		return false
	}
	// 带 self pickup filter 或者自提商家召回的，使用自提距离过滤
	if decision.IsSelfPickupFilter(traceInfo) || store.IsSelfPickupRecall() {
		// 配置
		if apollo.SearchApolloCfg.PolygonDistanceFilter == false {
			if uint64(math.Floor(store.Distance)) > store.SelfPickupDistance {
				return false
			}
			return true
		}
		// 没有多边形数据或者异常
		if store.StorePickupPolygon == nil {
			if uint64(math.Floor(store.Distance)) > store.SelfPickupDistance {
				return false
			}
			return true
		}
		if !(store.StorePickupPolygon.Contains(locPointLatitude, locPointLongitude)) {
			return false
		}
		// 这个100公里是必须的隐形条件
		if uint64(math.Floor(store.Distance)) > uint64(float64(constants.MaxPickupDistance)) {
			return false
		} else {
			return true
		}
	} else {
		return false
	}
}

func isActiveByPrepaidPolygon(traceInfo *traceinfo.TraceInfo, storeDistance, storeDeliveryDistance float64, locPointLatitude, locPointLongitude float64, storePolygon *polygon.Polygon) bool {
	// 配置
	if storeDeliveryDistance <= 0 {
		storeDeliveryDistance = float64(constants.MaxDeliveryDistance)
	}
	if apollo.SearchApolloCfg.PolygonDistanceFilter == false {
		if storeDistance > storeDeliveryDistance {
			return false
		}
		return true
	}
	// 没有多边形数据或者异常
	if storePolygon == nil {
		if storeDistance > storeDeliveryDistance {
			return false
		}
		return true
	}
	if !(storePolygon.Contains(locPointLatitude, locPointLongitude)) {
		return false
	}
	// 这个20公里是必须的隐形条件
	if uint64(math.Floor(storeDistance)) > uint64(constants.MaxDeliveryDistance) {
		return false
	}
	return true
}
