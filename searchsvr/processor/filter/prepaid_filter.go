package filter

import (
	"context"
	"fmt"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func PrepaidForBagFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) []*model.PrepaidInfo {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhrasePrepaidFilter, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenFilter, len(prepaids))
	}()

	prepaids = SalesEndTimeFilter(ctx, traceInfo, prepaids)
	prepaids = PrepaidPriceFilter(ctx, traceInfo, prepaids)
	return prepaids
}

func PrepaidLandingPageFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) []*model.PrepaidInfo {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhrasePrepaidFilter, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenFilter, len(prepaids))
	}()
	if len(prepaids) == 0 {
		return prepaids
	}
	prepaids = OnGoingFilter(ctx, traceInfo, prepaids)
	prepaids = PrepaidPriceFilter(ctx, traceInfo, prepaids)
	prepaids = ClosedStoreFilter(ctx, traceInfo, prepaids)
	prepaids = PrepaidDishOnSaleFilter(ctx, traceInfo, prepaids)
	prepaids = UserGroupFilter(ctx, traceInfo, prepaids)
	prepaids = MaxPurchaseLimitFilter(ctx, traceInfo, prepaids)
	prepaids = UserPurchaseLimitFilter(ctx, traceInfo, prepaids)
	prepaids = PrepaidDistanceFilter(ctx, traceInfo, prepaids)
	prepaids = DuplicateDishesFilter(ctx, traceInfo, prepaids)
	prepaids = DuplicatePrepaidFilter(ctx, traceInfo, prepaids)
	return prepaids
}

func PrepaidDistanceFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 || prepaid.StoreMeta == nil {
			continue
		}
		// 20km
		if prepaid.Distance > 20000 {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidMaxDistance, prepaid.Distance)
			continue
		}
		if cid.IsVN() {
			delDistance := 20000.0
			if prepaid.StoreMeta.GetIsFoodyDelivery() == 0 && prepaid.StoreMeta.GetOriginalDeliveryDistance() > 0 {
				delDistance = float64(prepaid.StoreMeta.GetOriginalDeliveryDistance())
			} else if prepaid.StoreMeta.GetIsFoodyDelivery() == 1 && prepaid.StoreMeta.GetDeliveryDistance() > 0 {
				delDistance = float64(prepaid.StoreMeta.GetDeliveryDistance())
			}
			if prepaid.Distance > delDistance {
				traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidDeliveryDistance, fmt.Sprintf("%f_%d", prepaid.Distance, delDistance))
				continue
			}
		} else {
			if isActiveByPrepaidPolygon(traceInfo, prepaid.Distance, float64(prepaid.StoreMeta.GetDeliveryDistance()), traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude, prepaid.StorePolygon) == false {
				traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidPolygon, fmt.Sprintf("%d_%f_%d", prepaid.StoreId, prepaid.Distance, prepaid.StoreMeta.GetDeliveryDistance()))
				continue
			}
		}
		resPrepaidIDs = append(resPrepaidIDs, prepaid)
	}
	return resPrepaidIDs
}

// landing page 需要过滤当前未开始和已结束的
// bag page 只需要过滤已结束的
func OnGoingFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	timeNowMil := uint64(traceInfo.SearchSysTime.UnixMilli()) // 毫秒级时间戳
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 {
			continue
		}
		if prepaid.PrepaidMeta.GetSalesStartTime() > timeNowMil || prepaid.PrepaidMeta.GetSalesEndTime() < timeNowMil {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidNotOnGoing, fmt.Sprintf("start:%d, end:%d, now:%d", prepaid.PrepaidMeta.GetSalesStartTime(), prepaid.PrepaidMeta.GetSalesEndTime(), timeNowMil))
			continue
		}
		resPrepaidIDs = append(resPrepaidIDs, prepaid)
	}
	return resPrepaidIDs
}

// landing page 需要过滤当前未开始和已结束的
// bag page 只需要过滤已结束的
func SalesEndTimeFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	timeNowMil := uint64(traceInfo.SearchSysTime.UnixMilli()) // 毫秒级时间戳
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 {
			continue
		}
		if prepaid.PrepaidMeta.GetSalesEndTime() < timeNowMil {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidSalesEndTime, fmt.Sprintf("start:%d, end:%d, now:%d", prepaid.PrepaidMeta.GetSalesStartTime(), prepaid.PrepaidMeta.GetSalesEndTime(), timeNowMil))
			continue
		}
		resPrepaidIDs = append(resPrepaidIDs, prepaid)
	}
	return resPrepaidIDs
}

func UserGroupFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	if traceInfo.UserId == 0 {
		return prepaids // 如果没有用户id，则不进行用户组过滤
	}
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	if traceInfo.UserGroupMap == nil {
		traceInfo.UserGroupMap = make(map[string]struct{})
	}
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 {
			continue
		}
		isActive := true
		if len(prepaid.PrepaidMeta.GetApplicableUserGroupName()) > 0 {
			if _, ok := traceInfo.UserGroupMap[prepaid.PrepaidMeta.GetApplicableUserGroupName()]; !ok {
				isActive = false
			}
		}
		if isActive {
			resPrepaidIDs = append(resPrepaidIDs, prepaid)
		} else {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidNotInUserGroup, prepaid.PrepaidMeta.GetApplicableUserGroupName())
		}
	}
	return resPrepaidIDs
}

func MaxPurchaseLimitFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 {
			continue
		}
		if prepaid.IsSoldOut == 1 {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidSoldOut, nil)
		} else {
			resPrepaidIDs = append(resPrepaidIDs, prepaid)
		}
	}
	return resPrepaidIDs
}

func UserPurchaseLimitFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 {
			continue
		}
		if prepaid.PrepaidMeta.GetUserPurchaseLimit() <= prepaid.UserHistorySalesVolume {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidUserPurchaseLimit, fmt.Sprintf("limit:%d, history:%d", prepaid.PrepaidMeta.GetUserPurchaseLimit(), prepaid.UserHistorySalesVolume))
		} else {
			resPrepaidIDs = append(resPrepaidIDs, prepaid)
		}
	}
	return resPrepaidIDs
}

// 一个prepaid下可能有多个dish，去重后只保留距离最近的一个
func DuplicateDishesFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	prepaidDishList := make(map[uint64]model.PrepaidInfos, len(prepaids)) // key 是prepaidId, value 是该prepaid下的不同的dish
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 || prepaid.DishId == 0 {
			continue
		}
		if _, ok := prepaidDishList[prepaid.PrepaidId]; !ok {
			prepaidDishList[prepaid.PrepaidId] = make([]*model.PrepaidInfo, 0)
		}
		prepaidDishList[prepaid.PrepaidId] = append(prepaidDishList[prepaid.PrepaidId], prepaid)
	}
	for _, dishList := range prepaidDishList {
		if len(dishList) == 0 {
			continue
		}
		if len(dishList) == 1 {
			resPrepaidIDs = append(resPrepaidIDs, dishList[0])
			continue
		}
		dishList.SortByDistance()
		resPrepaidIDs = append(resPrepaidIDs, dishList[0])
		dishId := dishList[0].DishId
		for i := 1; i < len(dishList); i++ {
			traceInfo.AddFilteredStore(dishList[i].PrepaidId, traceinfo.FilteredTypePrepaidDuplicateDish, fmt.Sprintf("%d_%f", dishId, dishList[i].Distance))
		}
	}
	return resPrepaidIDs
}

// 一个dish下可能有多个prepaid，去重后只保留折扣最大的一个
func DuplicatePrepaidFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	dishPrepaidList := make(map[uint64]model.PrepaidInfos, len(prepaids)) // key 是 dishId, value 是该dish下的不同的prepaid
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 || prepaid.DishId == 0 {
			continue
		}
		if _, ok := dishPrepaidList[prepaid.DishId]; !ok {
			dishPrepaidList[prepaid.DishId] = make([]*model.PrepaidInfo, 0)
		}
		dishPrepaidList[prepaid.DishId] = append(dishPrepaidList[prepaid.DishId], prepaid)
	}
	for _, prepaidList := range dishPrepaidList {
		if len(prepaidList) == 0 {
			continue
		}
		if len(prepaidList) == 1 {
			resPrepaidIDs = append(resPrepaidIDs, prepaidList[0])
			continue
		}
		prepaidList.SortByPrepaidDiscount()
		resPrepaidIDs = append(resPrepaidIDs, prepaidList[0])
		prepaidId := prepaidList[0].PrepaidId
		for i := 1; i < len(prepaidList); i++ {
			traceInfo.AddFilteredStore(prepaidList[i].PrepaidId, traceinfo.FilteredTypePrepaidDuplicatePrepaid, fmt.Sprintf("%d_%f", prepaidId, prepaidList[i].PrepaidDiscount))
		}
	}
	return resPrepaidIDs
}

func ClosedStoreFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 || prepaid.StoreMeta == nil {
			continue
		}
		if prepaid.StoreMeta.GetDisplayOpeningStatus() == o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_CLOSE {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidClosedStore, prepaid.StoreMeta.GetDisplayOpeningStatus())
			continue
		}
		resPrepaidIDs = append(resPrepaidIDs, prepaid)
	}
	return resPrepaidIDs
}

func PrepaidPriceFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 || prepaid.PrepaidMeta == nil {
			continue
		}
		if prepaid.PrepaidMeta.GetListPrice() <= 0 {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidPrice, prepaid.PrepaidMeta.GetListPrice())
			continue
		}
		resPrepaidIDs = append(resPrepaidIDs, prepaid)
	}
	return resPrepaidIDs
}

func PrepaidDishOnSaleFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	resPrepaidIDs := make([]*model.PrepaidInfo, 0, len(prepaids))
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.PrepaidId == 0 || prepaid.DishMeta == nil {
			continue
		}
		if prepaid.DishMeta.GetSaleStatus() == o2oalgo.DishSale_DISH_SALE_NOT_FOR_SALE {
			traceInfo.AddFilteredStore(prepaid.PrepaidId, traceinfo.FilteredTypePrepaidDishNotOnSale, prepaid.DishId)
			continue
		}
		resPrepaidIDs = append(resPrepaidIDs, prepaid)
	}
	return resPrepaidIDs
}
