package recall

import (
	"context"
	"strconv"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/data_server"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type DSPrepaidRecallConfig struct {
	RecallName      string
	RecallBid       uint64
	RecallFeatureId uint64
	RecallPriority  int
	RecallKeyType   string // 召回的key类型，userId/geohash4
}

var DSPrepaidRecallConfigs = []*DSPrepaidRecallConfig{
	{
		RecallName:      constants.SQSUserPrepaidOrderRedirection,
		RecallBid:       constants.SQSUserPrepaidOrderRedirectionBid,
		RecallFeatureId: constants.SQSUserPrepaidOrderRedirectionFeatureId,
		RecallPriority:  constants.RecallPriority_SQSUserPrepaidOrderRedirection,
		RecallKeyType:   constants.RecallKeyType_UserId,
	},
	{
		RecallName:      constants.SQSUserDishOrderRedirection,
		RecallBid:       constants.SQSUserDishOrderRedirectionBid,
		RecallFeatureId: constants.SQSUserDishOrderRedirectionFeatureId,
		RecallPriority:  constants.RecallPriority_SQSUserDishOrderRedirection,
		RecallKeyType:   constants.RecallKeyType_UserId,
	},
	{
		RecallName:      constants.SQSHotRecall,
		RecallBid:       constants.SQSHotRecallBid,
		RecallFeatureId: constants.SQSHotRecallFeatureId,
		RecallPriority:  constants.RecallPriority_SQSHotRecall,
		RecallKeyType:   constants.RecallKeyType_GeoHash4,
	},
	{
		RecallName:      constants.SQSHighDiscountRecall,
		RecallBid:       constants.SQSHighDiscountRecallBid,
		RecallFeatureId: constants.SQSHighDiscountRecallFeatureId,
		RecallPriority:  constants.RecallPriority_SQSHighDiscountRecall,
		RecallKeyType:   constants.RecallKeyType_GeoHash4,
	},
}

func RecallPrepaidIdsFromDataServerWithConfigs(ctx context.Context, traceInfo *traceinfo.TraceInfo, normalRecallPrepaidInfos model.PrepaidInfos) (map[int][]uint64, []uint64) {
	pt := time.Now()
	prepaidIds := make([]uint64, 0, 1000)
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseDataServerRecallPrepaids, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenRecallFromDataServer, len(prepaidIds))
	}()

	recallList := make([][]uint64, len(DSPrepaidRecallConfigs))
	wg := sync.WaitGroup{}
	for i, conf := range DSPrepaidRecallConfigs {
		wg.Add(1)
		goroutine.WithGo(ctx, conf.RecallName, func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			config := param[1].(DSPrepaidRecallConfig)
			recallList[index] = RecallPrepaidIdsFromDataServerWithConfig(ctx, traceInfo, config)
		}, i, *conf)
	}
	wg.Wait()
	priorityMap := make(map[int][]uint64, len(DSPrepaidRecallConfigs))

	prepaidIdSet := make(map[uint64]struct{}, 1000) // 用于自身召回去重
	// 用于normal去重
	normalPrepaidIdMap := make(map[uint64]bool, len(normalRecallPrepaidInfos))
	for _, p := range normalRecallPrepaidInfos {
		if p == nil || p.PrepaidId == 0 {
			continue
		}
		normalPrepaidIdMap[p.PrepaidId] = true
	}
	maxRecallSize := abtest.GetPrepaidFewFewResultRecallSizeConfig(ctx, traceInfo.AbParamClient, constants.SQSFewResultMaxSize)
	totalCount := 0
	for i, recallIds := range recallList {
		if len(recallIds) == 0 {
			continue
		}
		ids := make([]uint64, 0, len(recallIds))
		recallSize := abtest.GetPrepaidFewFewResultRecallSizeConfig(ctx, traceInfo.AbParamClient, DSPrepaidRecallConfigs[i].RecallName)
		count := 0
		for _, recallId := range recallIds {
			if recallId == 0 {
				continue
			}
			if _, exists := normalPrepaidIdMap[recallId]; exists {
				continue // 如果召回的ID在normalPrepaidIdMap中，则跳过
			}
			if _, exists := prepaidIdSet[recallId]; exists {
				continue // 如果召回的ID已经存在于prepaidIdSet中，则跳过
			}
			count++
			totalCount++
			if totalCount > maxRecallSize {
				logkit.FromContext(ctx).Info("Reached max recall size limit for", logkit.String("recallName", DSPrepaidRecallConfigs[i].RecallName), logkit.Int("max size", maxRecallSize))
				break // 达到最大召回大小限制
			}
			if count > recallSize {
				logkit.FromContext(ctx).Info("Reached recall size limit for", logkit.String("recallName", DSPrepaidRecallConfigs[i].RecallName), logkit.Int("size", recallSize))
				break // 达到召回大小限制
			}
			prepaidIdSet[recallId] = struct{}{}
			ids = append(ids, recallId)
			prepaidIds = append(prepaidIds, recallId)
		}
		if len(ids) > 0 {
			priorityMap[DSPrepaidRecallConfigs[i].RecallPriority] = ids
		}
	}
	return priorityMap, prepaidIds
}

func RecallPrepaidIdsFromDataServerWithConfig(ctx context.Context, traceInfo *traceinfo.TraceInfo, config DSPrepaidRecallConfig) []uint64 {
	pt := time.Now()
	var res []uint64
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseDataServerRecallPrepaids)+"_"+config.RecallName), time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenRecallFromDataServer+"_"+config.RecallName, len(res))
	}()
	if config.RecallKeyType == constants.RecallKeyType_UserId && traceInfo.UserId == 0 {
		logkit.FromContext(ctx).Info("userId is zero, skip recall for", logkit.String("recallName", config.RecallName))
		return []uint64{}
	}
	if config.RecallKeyType == constants.RecallKeyType_GeoHash4 && traceInfo.UserContext.GeoHash4 == "" {
		logkit.FromContext(ctx).Error("geohash4 is empty, skip recall for", logkit.String("recallName", config.RecallName))
		return []uint64{}
	}
	key := traceInfo.UserContext.GeoHash4
	if config.RecallKeyType == constants.RecallKeyType_UserId {
		key = strconv.FormatUint(traceInfo.UserId, 10)
	}
	data := data_server.GetFromDataServerSingleKeySingleFeature(ctx, traceInfo, key, config.RecallBid, config.RecallFeatureId)
	prepaidIds := data.GetInt64List().GetValue()
	if len(prepaidIds) == 0 {
		return []uint64{}
	}
	res = make([]uint64, 0, len(prepaidIds))
	for _, prepaidId := range prepaidIds {
		if prepaidId > 0 {
			res = append(res, uint64(prepaidId))
		}
	}
	return res
}
