package recall

import (
	"context"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

const PrepaidForBagRecallSize = 1000
const PrepaidDishesAggRecallSize = 1
const PrepaidDishesRecallSize = 500
const PrepaidRecallDistance = 20 //km

func (r *EsRecallRequest) BuildPrepaidForBagRecallDSL(traceInfo *traceinfo.TraceInfo) *model2.ESSearch {
	keyword := traceInfo.QueryKeyword
	if cid.IsTH() && traceInfo.QPResult != nil && len(traceInfo.QPResult.Segments) > 0 {
		keyword = util.BuildStrFromArr(traceInfo.QPResult.Segments)
	}
	keyword = strings.ToLower(keyword)
	should := []elastic.Query{
		elastic.NewMultiMatchQuery(keyword, "prepaid_name"),
		elastic.NewTermQuery("id", keyword),
	}
	filters := []elastic.Query{
		elastic.NewTermQuery("has_available_dish", 1),
		elastic.NewTermQuery("item_status", 1),
		elastic.NewBoolQuery().MustNot(elastic.NewTermQuery("exclude_sales_channel_ids", 2)), // 排除不能在直播渠道售卖的
		// landing page 需要过滤当前未开始和已结束的, bag page 只需要过滤已结束的
		//elastic.NewRangeQuery("sales_start_time").Lte(traceInfo.SearchSysTime.UnixMilli()),
		elastic.NewRangeQuery("sales_end_time").Gte(traceInfo.SearchSysTime.UnixMilli()),
	}

	if traceInfo.TraceRequest.StreamerInfo.GetStreamerType() == 1 {
		resIds := make([]interface{}, 0, len(traceInfo.TraceRequest.StreamerInfo.GetRestaurantIds()))
		for _, b := range traceInfo.TraceRequest.StreamerInfo.GetRestaurantIds() {
			resIds = append(resIds, b)
		}
		if len(resIds) > 0 {
			filters = append(filters, elastic.NewTermsQuery("store_ids", resIds...))
		}

		merchantIds := make([]interface{}, 0, len(traceInfo.TraceRequest.StreamerInfo.GetMerchantIds()))
		for _, m := range traceInfo.TraceRequest.StreamerInfo.GetMerchantIds() {
			merchantIds = append(merchantIds, m)
		}
		if len(merchantIds) > 0 {
			filters = append(filters, elastic.NewTermsQuery("merchant_ids", merchantIds...))
		}
	}

	sorts := []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewFieldSort("_id").Asc(),
	}

	dsl := es.NewESSearch(
		es.WithShouldQueries(should),
		es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)),
		es.WithSize(PrepaidForBagRecallSize),
		es.WithSorters(sorts),
		es.WithSourceInclude("id", "max_dish_price"), // prepaid 正排信息无max_dish_price，需要从ES返回
		es.WithRecallType("BuildPrepaidForBagRecallDSL"),
	)
	logkit.Debug("EsRecallRequest.BuildPrepaidForBagRecallDSL", zap.Any("dsl", dsl))
	return dsl
}

func (r *EsRecallRequest) BuildPrepaidDishesRecallDSL(ctx context.Context, traceInfo *traceinfo.TraceInfo, keyword string, prepaidIds []uint64) *model2.ESSearch {
	var queryStoreIntention, queryDishIntention string
	if traceInfo.QPResult != nil {
		queryStoreIntention = traceInfo.QPResult.QueryStoreIntention
		queryDishIntention = traceInfo.QPResult.QueryDishIntention
	}
	prepaidNameBoot, storeNameBoot, dishNameBoot, brandNameBoot := abtest.GetPrepaidQueryIntentionBoostConfig(ctx, traceInfo.AbParamClient, queryStoreIntention, queryDishIntention)
	should := []elastic.Query{}
	if len(keyword) > 0 {
		if cid.IsTH() && traceInfo.QPResult != nil && len(traceInfo.QPResult.Segments) > 0 {
			keyword = util.BuildStrFromArr(traceInfo.QPResult.Segments)
		}
		keyword = strings.ToLower(keyword)
		should = append(should, elastic.NewMatchQuery("prepaid_name", keyword).Operator("and").Boost(prepaidNameBoot))
		should = append(should, elastic.NewMatchQuery("store_name", keyword).Operator("and").Boost(storeNameBoot))
		should = append(should, elastic.NewMatchQuery("dish_name", keyword).Operator("and").Boost(dishNameBoot))
		should = append(should, elastic.NewMatchQuery("brand_name", keyword).Operator("and").Boost(brandNameBoot))
		//must = append(must, elastic.NewMultiMatchQuery(keyword, "prepaid_name", "store_name", "dish_name", "brand_name"))
	}
	filters := []elastic.Query{}
	topLeft, bottomRight := model.CalBox(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude, PrepaidRecallDistance)
	if len(prepaidIds) > 0 {
		pIds := make([]interface{}, 0, len(prepaidIds))
		for _, pId := range prepaidIds {
			pIds = append(pIds, pId)
		}
		filters = append(filters, elastic.NewTermsQuery("prepaid_id", pIds...))
	}

	filters = append(filters, elastic.NewTermsQuery("price_diff_flag", 1)) // item_original_price - prepaid_purchase_price >= 0
	// landing page 需要过滤当前未开始和已结束的, bag page 只需要过滤已结束的
	filters = append(filters, elastic.NewRangeQuery("sales_start_time").Lte(traceInfo.SearchSysTime.UnixMilli()))
	filters = append(filters, elastic.NewRangeQuery("sales_end_time").Gte(traceInfo.SearchSysTime.UnixMilli()))
	filters = append(filters, elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"))
	filters = append(filters, elastic.NewBoolQuery().MustNot(elastic.NewTermQuery("exclude_sales_channel_ids", 1))) // 排除不能在普通渠道售卖的

	prepaidDishesAggRecallSize := abtest.GetPrepaidDishesAggRecallSize(ctx, traceInfo.AbParamClient)
	prepaidDishesRecallSize := abtest.GetPrepaidDishesRecallSize(ctx, traceInfo.AbParamClient)
	aggTopHist := elastic.NewTopHitsAggregation().Size(prepaidDishesAggRecallSize).
		SortBy(elastic.NewGeoDistanceSort("location").Point(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude).Asc()).
		Sort("_score", false).Sort("prepaid_price", false).Sort("dish_id", false).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id", "prepaid_id", "store_id", "dish_id")).TrackScores(true)

	aggs := elastic.NewTermsAggregation().Field("prepaid_id").Size(prepaidDishesRecallSize).SubAggregation("top_dishes", aggTopHist)

	sorts := []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewGeoDistanceSort("location").Point(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude).Asc(),
		elastic.NewFieldSort("prepaid_sales_volume").Desc(),
	}
	dsl := es.NewESSearch(
		es.WithTermsAggregation(aggs),
		es.WithShouldQueries(should),
		es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)),
		es.WithSize(0),
		es.WithSorters(sorts),
		es.WithRecallType("BuildPrepaidDishesRecallDSL"),
	)
	logkit.Debug("EsRecallRequest.BuildPrepaidDishesRecallDSL", zap.Any("dsl", dsl))
	return dsl
}
