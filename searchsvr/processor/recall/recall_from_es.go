package recall

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/merge"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"
)

func recallStoreFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*model.RecallStore, error) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseStoreRecallMultiSourceES, time.Since(pt))
	}()
	searchReq := NewESRecallRequest(traceInfo)

	searchReqInfo := searchReq.BuildRecallDSLByConfig(ctx, traceInfo)
	// 没有召回列表则再走一次兜底 hardCode
	if searchReqInfo == nil {
		// 少无结果如果无结果则直接返回，不需要兜底recalls
		if traceInfo.PipelineType == traceinfo.PipelineTypeSearchFewResult {
			return nil, nil
		}
		searchReqInfo = createDefaultRecallConfigs(ctx, traceInfo, searchReq)
	}

	// 获取es dsl cache key
	var recallList []*model.RecallStore
	// 多路召回，未合并
	var err error
	recallList, err = RecallStoresFromES(ctx, traceInfo, searchReqInfo, searchReqInfo.StoreQueries)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("searchDao.SearchStores error")
		return nil, err
	}
	return recallList, nil
}

func createDefaultRecallConfigs(ctx context.Context, traceInfo *traceinfo.TraceInfo, searchReq *EsRecallRequest) *StoreRecall {
	defaultStoreRecall := &StoreRecall{}
	if cid.IsID() && traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
		defaultStoreRecall = searchReq.BuildRecallDSLByCodeID(ctx, traceInfo)
	}

	if (cid.IsTH() || cid.IsMY()) && traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
		defaultStoreRecall = searchReq.BuildRecallDSLByCodeTHMY(ctx, traceInfo)
	}

	if cid.IsVN() && traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
		defaultStoreRecall = searchReq.BuildRecallDSLByCodeVN(ctx, traceInfo)
	}

	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchMainSite {
		defaultStoreRecall = searchReq.BuildRecallDSLByCodeIDMainSite(ctx, traceInfo)
	}

	return defaultStoreRecall
}

func RecallStoresFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeRecall *StoreRecall, reqs []*es.ESSearch) ([]*model.RecallStore, error) {
	eSClient := traceinfo.GetESClientFromPool(ctx, traceInfo, traceinfo.StoreES)
	parallel := len(reqs)
	if parallel == 0 {
		return []*model.RecallStore{}, nil
	}
	errs := make(chan error, parallel)
	recallList := make([]*model.RecallStore, parallel)

	// debug 模式下方便查看 dsl
	if traceInfo.IsDebug {
		for _, req := range reqs {
			traceInfo.SetPhraseStoreESDsl(req.RecallType+"_"+req.RecallTypeName+"_"+req.RecallId, req.DslString(ctx))
		}
	}

	wg := &sync.WaitGroup{}
	wg.Add(parallel)
	for i, req := range reqs {
		go func(index int, esSearch *es.ESSearch) {
			startTime := time.Now()
			recallStore := &model.RecallStore{}
			defer wg.Done()
			defer func() {
				traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseStoreRecallMultiSourceES)+"_"+esSearch.RecallType+"_"+esSearch.RecallId), time.Since(startTime))
				if e := recover(); e != nil {
					logkit.Error("search es panic", logkit.String("recall type", esSearch.RecallType), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
					reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
						Key: "method",
						Val: "search-es-store-" + esSearch.RecallType,
					})
				}
			}()

			totalHits, stores, err := SearchStoreES(ctx, traceInfo, eSClient, esSearch)
			if traceInfo.IsDebug {
				logkit.FromContext(ctx).Info("search es store result", logkit.String("recallName", esSearch.RecallTypeName), logkit.String("recallType", esSearch.RecallType), logkit.Int("len of stores", len(stores)), logkit.String("dsl", esSearch.DslString(ctx)))
			} else {
				logkit.FromContext(ctx).Debug("search es store result", logkit.String("recallName", esSearch.RecallTypeName), logkit.String("recallType", esSearch.RecallType), logkit.Int("len of stores", len(stores)), logkit.String("dsl", esSearch.DslString(ctx)))
			}

			if err != nil {
				traceInfo.AddErrorToTraceInfo(err)
				logkit.FromContext(ctx).WithError(err).Error("SearchStoreDao.Search search stores failed", logkit.String("recall type", esSearch.RecallType), logkit.String("cost", time.Since(startTime).String()), logkit.Any("es search", esSearch))
				errs <- err
			} else {
				recallConfig := storeRecall.StoreRecallConfigs[index]
				// 是否召回菜
				isNeedDish := isNeedRecallDish(ctx, traceInfo, storeRecall.StoreRecallConfigs[index])
				// 是否 my，th 地区减分
				isNeedMinus := isNeedMinusForMyTh(ctx, traceInfo, storeRecall.StoreRecallConfigs[index])

				for pos, s := range stores {
					s.RecallScores = []float64{s.ESScore}
					if isNeedMinus {
						s.ESScore = s.ESScore - 1.0
					}
					s.Score = s.ESScore
					s.RecallTypes = []string{recallConfig.RecallTypeStr}
					s.RecallTypeAndIds = []string{fmt.Sprintf("%s_%s", recallConfig.RecallTypeStr, recallConfig.RecallId)}
					s.RecallPosList = []string{fmt.Sprintf("%s_%d", recallConfig.RecallTypeStr, pos)}
					s.RecallQueries = []string{esSearch.Query}
					s.RelLevelFromRecall = recallConfig.RelLevelFromRecall
					s.IsNeedDishRecall = isNeedDish
					s.RecallSuppressType = recallConfig.RecallSuppressType
					// 配置化过滤 PRelevanceScore
					if recallConfig.FilterByRelevanceScoreSwitch {
						s.FilterRecallConfigPRelevanceScore = recallConfig.FilterByRelevanceScore
					} else {
						s.FilterRecallConfigPRelevanceScore = model.DefaultNonConfiguredPRelevanceScore
					}
					// 配置化过滤 Distance
					if recallConfig.FilterByDistanceSwitch {
						s.FilterRecallConfigDistance = recallConfig.FilterByDistance
					}
					switch recallConfig.RecallTypeStr {
					case "StoreIntervention":
						s.StoreInterventionRecall = 1 // 干预store id, brand id, query 召回
					case "StoreInterventionWithMerchantID":
						s.StoreInterventionWithMerchantIDRecall = 1 // 干预 merchant id 召回
					}
				}
				// TH,MY 站内，ID 主站需要直接在storeWithDishField里召菜
				if recallConfig.RecallType == int32(foodalgo_search.RecallType_StoreWithDishField) && len(stores) > 0 &&
					(env.GetCID() == cid.MY || env.GetCID() == cid.TH) {
					if decision.IsUseDishRecallV2(traceInfo) == true {
						// 新挂菜listing不用 th/my 二阶段挂菜，pass
						logkit.FromContext(ctx).Debug("skip TH,MY dish recall for new dish listing")
					} else {
						dishes, _ := RecallDishFromES(ctx, traceInfo, stores, false)
						dishInfos, _ := filling.DishMetaFilling(ctx, traceInfo, dishes) // es 召回没有返回store id,需要查询正排获取
						stores = merge.TransDishStoreForTHMY(dishInfos)
					}
				}
				recallStore.Stores = stores
				recallStore.TotalHits = totalHits
				recallStore.RecallTypeStr = recallConfig.RecallTypeStr
				recallStore.RelLevelFromRecall = recallConfig.RelLevelFromRecall
				recallStore.IsNeedDishRecall = isNeedDish
				recallStore.RecallId = recallConfig.RecallId
				recallList[index] = recallStore
			}
		}(i, req)
	}
	wg.Wait()
	if len(errs) == parallel {
		// 全部召回都报错才抛错
		return nil, <-errs
	}
	return recallList, nil
}

func RecallDishesFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, req *es.ESSearch) (model.DishInfos, error) {
	// debug 模式下方便查看 dsl
	if traceInfo.IsDebug {
		traceInfo.SetPhraseDishESDsl(req.RecallTypeName+"_"+req.RecallId, req.DslString(ctx))
	}

	eSClient := traceinfo.GetESClientFromPool(ctx, traceInfo, traceinfo.DishES)
	_, dishes, err := SearchDishES(ctx, traceInfo, eSClient, req, traceInfo.UserId)
	return dishes, err
}

func RecallMultiDishesFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, reqs []*es.ESSearch) ([]*model.RecallDish, error) {
	// debug 模式下方便查看 dsl
	if traceInfo.IsDebug {
		for _, req := range reqs {
			traceInfo.SetPhraseDishESDsl(req.RecallTypeName+"_"+req.RecallId, req.DslString(ctx))
		}
	}

	eSClient := traceinfo.GetESClientFromPool(ctx, traceInfo, traceinfo.DishES)
	parallel := len(reqs)
	if parallel == 0 {
		return []*model.RecallDish{}, nil
	}
	errs := make(chan error, parallel)
	recallList := make([]*model.RecallDish, parallel)

	wg := &sync.WaitGroup{}
	wg.Add(parallel)
	for i, req := range reqs {
		go func(index int, esSearch *es.ESSearch) {
			startTime := time.Now()
			recallDish := &model.RecallDish{}
			defer wg.Done()
			defer func() {
				traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseDishListingMultiRecallES)+"_"+esSearch.RecallType+"_"+esSearch.RecallId), time.Since(startTime))
				if e := recover(); e != nil {
					logkit.Error("search es panic", logkit.String("recall type", esSearch.RecallType), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
					reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
						Key: "method",
						Val: "search-es-dish-" + esSearch.RecallType,
					})
				}
			}()

			totalHits, dishes, err := SearchDishES(ctx, traceInfo, eSClient, esSearch, traceInfo.UserId)
			if traceInfo.IsDebug {
				logkit.FromContext(ctx).Info("search es dish result", logkit.String("recallName", esSearch.RecallTypeName), logkit.String("recallType", esSearch.RecallType), logkit.Int("len of dishes", len(dishes)), logkit.String("dsl", esSearch.DslString(ctx)), logkit.Int64("cost", time.Since(startTime).Milliseconds()))
			}

			if err != nil {
				traceInfo.AddErrorToTraceInfo(err)
				logkit.FromContext(ctx).WithError(err).Error("SearchStoreDao.Search search dishes failed", logkit.String("recall type", esSearch.RecallType), logkit.String("cost", time.Since(startTime).String()), logkit.Any("es search dsl", esSearch.DslString(ctx)))
				errs <- err
			} else {
				recallConfig := esSearch.RecallConfig
				if recallConfig != nil {
					recallDish.RecallTypeStr = recallConfig.RecallTypeStr
					recallDish.RecallId = recallConfig.RecallId
					recallDish.RecallPriority = recallConfig.RecallPriority
				}
				for _, s := range dishes {
					s.Score = s.ESScore
				}
				recallDish.Dishes = dishes
				recallDish.TotalHits = totalHits
				recallList[index] = recallDish
			}
		}(i, req)
	}
	wg.Wait()
	if len(errs) == parallel {
		// 全部召回都报错才抛错
		return nil, <-errs
	}
	return recallList, nil
}

func RecallStoreFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, req *es.ESSearch) (model.StoreInfos, error) {
	// debug 模式下方便查看 dsl
	if traceInfo.IsDebug {
		traceInfo.SetPhraseStoreESDsl(req.RecallType+"_"+req.RecallId, req.DslString(ctx))
	}

	eSClient := traceinfo.GetESClientFromPool(ctx, traceInfo, traceinfo.StoreES)
	_, stores, err := SearchStoreES(ctx, traceInfo, eSClient, req)
	return stores, err
}

func RecallL3CategoryFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, esSearch *es.ESSearch) (uint64, []uint32, error) {
	eSClient := traceinfo.GetESClientFromPool(ctx, traceInfo, traceinfo.DishES)
	total, categories, err := eSClient.SearchCategoryAgg(ctx, esSearch, traceInfo.UserId)
	return total, categories, err
}

func isNeedRecallDish(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig) bool {
	// 是否挂菜
	if len(recallConfig.DishRecallCondition) == 0 {
		return false
	}
	ok, err := parse.CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, &recallConfig.DishRecallCondition)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration isNeedRecallDish failed", logkit.String("recallType", recallConfig.RecallLogName))
		return false
	}
	return ok
}

func isNeedMinusForMyTh(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig) bool {
	// my, th 地区要减分
	if !(env.GetCID() == cid.MY || env.GetCID() == cid.TH) {
		return false
	}
	// 排除部分召回
	if recallConfig.RecallTypeStr == "StoreIntervention" || recallConfig.RecallTypeStr == "StoreInterventionWithMerchantID" {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration isNeedMinusForMyTh false", logkit.String("recallType", recallConfig.RecallTypeStr), logkit.String("recallName", recallConfig.RecallLogName))
		return false
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration isNeedMinusForMyTh true", logkit.String("recallType", recallConfig.RecallTypeStr), logkit.String("recallName", recallConfig.RecallLogName))
	return true
}

func RecallPrepaidFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, req *es.ESSearch) (model.PrepaidInfos, error) {
	// debug 模式下方便查看 dsl
	if traceInfo.IsDebug {
		traceInfo.SetPhraseStoreESDsl(req.RecallType+"_"+req.RecallId, req.DslString(ctx))
	}

	eSClient := traceinfo.GetESClientFromPool(ctx, traceInfo, traceinfo.PrepaidES)
	prepaids, err := SearchPrepaidES(ctx, traceInfo, eSClient, req)
	return prepaids, err
}

func RecallPrepaidDishesFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, req *es.ESSearch) (model.PrepaidInfos, error) {
	// debug 模式下方便查看 dsl
	if traceInfo.IsDebug {
		traceInfo.SetPhraseStoreESDsl(req.RecallType+"_"+req.RecallId, req.DslString(ctx))
	}

	eSClient := traceinfo.GetESClientFromPool(ctx, traceInfo, traceinfo.PrepaidDishES)
	prepaids, err := SearchPrepaidDishES(ctx, traceInfo, eSClient, req)
	return prepaids, err
}
