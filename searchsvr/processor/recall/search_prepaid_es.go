package recall

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func SearchPrepaidES(ctx context.Context, traceInfo *traceinfo.TraceInfo, esBase *es.ESBase, esSearch *es.ESSearch) ([]*model.PrepaidInfo, error) {
	// 优先从召回配置化获取timeout
	timeout := esSearch.RecallTimeoutMs
	if timeout <= 0 {
		timeout = apollo.SearchApolloCfg.ClientTimeOutConfig.SearchPrepaidTimeOut
		if timeout <= 0 {
			timeout = 500
		}
	}
	if env.GetEnv() == "liveish" || env.GetEnv() == "test" {
		timeout = 5000
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	startTime := time.Now()
	_, hits, err, _ := esBase.Search(ctx, esSearch, traceInfo.UserId)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("SearchPrepaidES esBase.Search failed", logkit.String("cost", time.Since(startTime).String()), logkit.String("recallId", esSearch.RecallId), logkit.String("recallType", esSearch.RecallTypeName), logkit.String("dsl", esSearch.DslString(ctx)))
		return nil, err
	}
	nPrepaids := len(hits)
	prepaids := make([]*model.PrepaidInfo, 0, nPrepaids)
	for _, hit := range hits {
		if hit == nil {
			continue
		}
		id, err1 := strconv.ParseUint(hit.Id, 10, 64)
		if err1 != nil {
			continue
		}
		if len(hit.Source) == 0 {
			logkit.FromContext(ctx).Error("SearchPrepaidES hit.Source is empty, need to check", logkit.String("hitId", hit.Id), logkit.String("source", string(hit.Source)))
			continue
		}
		prepaidES := &model.PrepaidSearchES{}
		err = json.Unmarshal(hit.Source, prepaidES)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("SearchPrepaidES json.Unmarshal hit.Source failed", logkit.String("hitId", hit.Id), logkit.String("source", string(hit.Source)))
			continue
		}
		prepaidInfo := &model.PrepaidInfo{
			PrepaidId:    id,
			MaxDishPrice: prepaidES.GetMaxDishPrice(),
		}
		score := float64(0)
		if hit.Score != nil {
			score = *hit.Score
		}
		if score == 0.0 && len(hit.Sort) != 0 {
			if s, ok := hit.Sort[0].(float64); ok {
				score = s
			}
		}
		prepaidInfo.ESScore = score

		// es explain
		var explainStr string
		if traceInfo.IsNeedEsExplain && traceInfo.EsExplainStoreIds[id] == true && hit.Explanation != nil {
			explanationJSON, err2 := json.Marshal(hit.Explanation)
			if err2 != nil {
				fmt.Printf("Error marshaling es explanation to JSON: %s\n", err2)
			} else {
				explainStr = string(explanationJSON)
				prepaidInfo.EsExplains = []string{explainStr}
			}
		}
		prepaids = append(prepaids, prepaidInfo)
	}
	return prepaids, nil
}

func SearchPrepaidDishES(ctx context.Context, traceInfo *traceinfo.TraceInfo, esBase *es.ESBase, esSearch *es.ESSearch) ([]*model.PrepaidInfo, error) {
	// 优先从召回配置化获取timeout
	timeout := esSearch.RecallTimeoutMs
	if timeout <= 0 {
		timeout = apollo.SearchApolloCfg.ClientTimeOutConfig.SearchPrepaidTimeOut
		if timeout <= 0 {
			timeout = 500
		}
	}
	if env.GetEnv() == "liveish" || env.GetEnv() == "test" {
		timeout = 5000
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	startTime := time.Now()
	_, hits, err, _ := esBase.Search(ctx, esSearch, traceInfo.UserId)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("SearchPrepaidDishES esBase.Search failed", logkit.String("cost", time.Since(startTime).String()), logkit.String("recallId", esSearch.RecallId), logkit.String("recallType", esSearch.RecallTypeName), logkit.String("dsl", esSearch.DslString(ctx)))
		return nil, err
	}
	nPrepaids := len(hits)
	prepaids := make([]*model.PrepaidInfo, 0, nPrepaids)
	for _, hit := range hits {
		if hit == nil {
			continue
		}
		// prepaid dish 索引的 _id 字段是mapping 自增id, 无意义。使用source中的prepaidId
		//id, err2 := strconv.ParseUint(hit.Id, 10, 64)
		//if err2 != nil {
		//	continue
		//}
		if len(hit.Source) == 0 {
			logkit.FromContext(ctx).Error("SearchPrepaidDishES hit.Source is empty, need to check", logkit.String("hitId", hit.Id), logkit.String("source", string(hit.Source)))
			continue
		}
		prepaidES := &model.PrepaidSearchES{}
		err = json.Unmarshal(hit.Source, prepaidES)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("SearchPrepaidDishES json.Unmarshal hit.Source failed", logkit.String("hitId", hit.Id), logkit.String("source", string(hit.Source)))
			continue
		}
		prepaidInfo := &model.PrepaidInfo{
			PrepaidId: prepaidES.GetPrepaidId(),
			StoreId:   prepaidES.GetStoreId(),
			DishId:    prepaidES.GetDishId(),
		}
		score := float64(0)
		if hit.Score != nil {
			score = *hit.Score
		}
		if score == 0.0 && len(hit.Sort) != 0 {
			score = hit.Sort[0].(float64)
		}
		prepaidInfo.ESScore = score
		// es explain
		var explainStr string
		if traceInfo.IsNeedEsExplain && traceInfo.EsExplainStoreIds[prepaidES.GetPrepaidId()] == true && hit.Explanation != nil {
			explanationJSON, err1 := json.Marshal(hit.Explanation)
			if err1 != nil {
				fmt.Printf("Error marshaling es explanation to JSON: %s\n", err1)
			} else {
				explainStr = string(explanationJSON)
				prepaidInfo.EsExplains = []string{explainStr}
			}
		}
		prepaids = append(prepaids, prepaidInfo)
	}
	return prepaids, nil
}
