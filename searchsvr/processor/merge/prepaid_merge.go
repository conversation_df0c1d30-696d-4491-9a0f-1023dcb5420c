package merge

import (
	"context"
	"sort"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 按照各路召回保留、候补填充进行召回融合截断

func PrepaidsMergeAndTruncate(ctx context.Context, traceInfo *traceinfo.TraceInfo, esRecallPrepaids model.PrepaidInfos, dsRecallPrepaidsList map[int][]uint64) model.PrepaidInfos {
	var res model.PrepaidInfos
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseMergeAndTruncatePrepaids, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenMergeAndTruncatePrepaids, len(res))
	}()

	// 从ES召回的prepaid 优先级
	priorityMap := priorityPrepaidsMap(esRecallPrepaids, dsRecallPrepaidsList)
	// 最大size
	maxSize := abtest.GetPrepaidFewFewResultPriorityGuaranteedMaxSizeConfig(ctx, traceInfo.AbParamClient)
	if len(esRecallPrepaids) <= maxSize {
		// 如果ES召回的prepaid数量小于最大size，直接返回. 这个需要在priorityPrepaidsMap后处理，用于填充priority信息
		res = esRecallPrepaids
		return res
	}
	// 获取优先级保量quota
	quotaMap := GetPriorityQuota(ctx, traceInfo, dsRecallPrepaidsList)
	res = mergeWithQuota(priorityMap, quotaMap, maxSize)

	// 检查被过滤的prepaid信息
	resSet := res.PrepaidIdsSet()
	for _, prepaidInfo := range esRecallPrepaids {
		if prepaidInfo == nil || prepaidInfo.PrepaidId == 0 {
			continue
		}
		if _, exists := resSet[prepaidInfo.PrepaidId]; !exists {
			// 如果es召回的prepaid不在结果中，说明被过滤了
			traceInfo.AddFilteredStore(prepaidInfo.PrepaidId, traceinfo.FilteredTypePrepaidRecallSizeLimit, nil)
		}
	}
	return res
}

func mergeWithQuota(priorityPrepaidsMap map[int][]*model.PrepaidInfo, quotaMap map[int]int, maxSize int) []*model.PrepaidInfo {
	priorities := make([]int, 0, 4)
	for priority := range priorityPrepaidsMap {
		priorities = append(priorities, priority)
	}
	sort.Ints(priorities)
	selected := make([]*model.PrepaidInfo, 0, maxSize)
	// 初始化每个优先级的当前读取下标
	pointers := make(map[int]int)
	// Step 1: 保量阶段
	for _, priority := range priorities {
		prepaidInfos := priorityPrepaidsMap[priority]
		quota := quotaMap[priority]
		if quota > len(prepaidInfos) {
			quota = len(prepaidInfos)
		}
		selected = append(selected, prepaidInfos[:quota]...)
		// 初始化每个指针到保量之后的位置
		pointers[priority] = quota
	}
	if len(selected) >= maxSize {
		// 如果保量阶段已经达到最大限制，直接截断返回
		return selected[:maxSize]
	}
	// Step 2: 轮询补全阶段
	isFinish := false
	for len(selected) < maxSize && !isFinish {
		isFinish = true // 假设是已经完成了。只有成功 append 后才置 false
		for _, p := range priorities {
			ptr := pointers[p]
			prepaidInfos := priorityPrepaidsMap[p]
			if ptr < len(prepaidInfos) {
				selected = append(selected, prepaidInfos[ptr])
				pointers[p]++
				isFinish = false
				if len(selected) >= maxSize {
					break
				}
			}
		}
	}
	return selected
}

func priorityPrepaidsMap(esRecallPrepaids model.PrepaidInfos, dsRecallPrepaidsList map[int][]uint64) map[int][]*model.PrepaidInfo {
	priorityMap := make(map[int][]*model.PrepaidInfo, 4)
	prepaidMap := esRecallPrepaids.PrepaidMap()
	for priority, ids := range dsRecallPrepaidsList {
		if len(ids) == 0 {
			continue
		}
		for _, id := range ids {
			if id == 0 {
				continue
			}
			if prepaidInfo, exists := prepaidMap[id]; exists {
				if len(priorityMap[priority]) == 0 {
					priorityMap[priority] = make([]*model.PrepaidInfo, 0, len(ids))
				}
				prepaidInfo.RecallPriority = priority // 设置召回优先级
				priorityMap[priority] = append(priorityMap[priority], prepaidInfo)
			}
		}
	}
	return priorityMap
}

func GetPriorityQuota(ctx context.Context, traceInfo *traceinfo.TraceInfo, dsRecallPrepaidsList map[int][]uint64) map[int]int {
	quotaMap := make(map[int]int, 4) // quotaMap[priority] = quota
	for priority, ids := range dsRecallPrepaidsList {
		if len(ids) == 0 {
			continue
		}
		if _, exists := quotaMap[priority]; !exists {
			quotaMap[priority] = abtest.GetPrepaidFewFewResultPriorityGuaranteedSizeConfig(ctx, traceInfo.AbParamClient, priority)
		}
	}
	return quotaMap
}
