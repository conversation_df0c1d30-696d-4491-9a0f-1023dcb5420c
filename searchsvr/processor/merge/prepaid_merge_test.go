package merge

import (
	"testing"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/stretchr/testify/assert"
)

func TestMergeWithQuota_EmptyInputs(t *testing.T) {
	result := mergeWithQuota(map[int][]*model.PrepaidInfo{}, map[int]int{}, 10)
	assert.Empty(t, result)
}

func TestMergeWithQuota_SinglePriorityEnoughQuota(t *testing.T) {
	infos := make([]*model.PrepaidInfo, 100)
	for i := 0; i < 100; i++ {
		infos[i] = &model.PrepaidInfo{PrepaidId: uint64(i + 1)}
	}
	priorityMap := map[int][]*model.PrepaidInfo{1: infos}
	quotaMap := map[int]int{1: 50}
	result := mergeWithQuota(priorityMap, quotaMap, 100)
	assert.Len(t, result, 100)
}

func TestMergeWithQuota_MultiPriorityQuotaAndRoundRobin(t *testing.T) {
	infos1 := make([]*model.PrepaidInfo, 60)
	infos2 := make([]*model.PrepaidInfo, 60)
	for i := 0; i < 60; i++ {
		infos1[i] = &model.PrepaidInfo{PrepaidId: uint64(i + 1)}
		infos2[i] = &model.PrepaidInfo{PrepaidId: uint64(i + 101)}
	}
	priorityMap := map[int][]*model.PrepaidInfo{1: infos1, 2: infos2}
	quotaMap := map[int]int{1: 30, 2: 20}
	result := mergeWithQuota(priorityMap, quotaMap, 100)
	assert.Len(t, result, 100)
	assert.Equal(t, infos1[:30], result[:30])
	assert.Equal(t, infos2[:20], result[30:50])
	// 轮询补全
	remaining := result[50:]
	for i, p := range remaining {
		if i%2 == 0 {
			assert.Contains(t, infos1[30:], p)
		} else {
			assert.Contains(t, infos2[20:], p)
		}
	}
}

func TestMergeWithQuota_QuotaExceedsAvailable(t *testing.T) {
	infos := make([]*model.PrepaidInfo, 10)
	for i := 0; i < 10; i++ {
		infos[i] = &model.PrepaidInfo{PrepaidId: uint64(i + 1)}
	}
	priorityMap := map[int][]*model.PrepaidInfo{1: infos}
	quotaMap := map[int]int{1: 20}
	result := mergeWithQuota(priorityMap, quotaMap, 15)
	assert.Len(t, result, 10)
}

func TestMergeWithQuota_MaxSizeZero(t *testing.T) {
	infos := make([]*model.PrepaidInfo, 10)
	for i := 0; i < 10; i++ {
		infos[i] = &model.PrepaidInfo{PrepaidId: uint64(i + 1)}
	}
	priorityMap := map[int][]*model.PrepaidInfo{1: infos}
	quotaMap := map[int]int{1: 5}
	result := mergeWithQuota(priorityMap, quotaMap, 0)
	assert.Empty(t, result)
}

func TestMergeWithQuota_QuotaZero(t *testing.T) {
	infos := make([]*model.PrepaidInfo, 10)
	for i := 0; i < 10; i++ {
		infos[i] = &model.PrepaidInfo{PrepaidId: uint64(i + 1)}
	}
	priorityMap := map[int][]*model.PrepaidInfo{1: infos}
	quotaMap := map[int]int{1: 0}
	result := mergeWithQuota(priorityMap, quotaMap, 10)
	assert.Len(t, result, 10)
}

func TestMergeWithQuota_LargeInput_NoDeadlock(t *testing.T) {
	infos1 := make([]*model.PrepaidInfo, 1000)
	infos2 := make([]*model.PrepaidInfo, 1000)
	for i := 0; i < 1000; i++ {
		infos1[i] = &model.PrepaidInfo{PrepaidId: uint64(i + 1)}
		infos2[i] = &model.PrepaidInfo{PrepaidId: uint64(i + 1001)}
	}
	priorityMap := map[int][]*model.PrepaidInfo{1: infos1, 2: infos2}
	quotaMap := map[int]int{1: 500, 2: 500}
	result := mergeWithQuota(priorityMap, quotaMap, 1500)
	assert.Len(t, result, 1500)
}

func TestMergeWithQuota_DuplicateIds(t *testing.T) {
	infos := []*model.PrepaidInfo{
		{PrepaidId: 1},
		{PrepaidId: 1},
		{PrepaidId: 2},
	}
	priorityMap := map[int][]*model.PrepaidInfo{1: infos}
	quotaMap := map[int]int{1: 2}
	result := mergeWithQuota(priorityMap, quotaMap, 3)
	assert.Len(t, result, 3)
	assert.Equal(t, uint64(1), result[0].PrepaidId)
	assert.Equal(t, uint64(1), result[1].PrepaidId)
	assert.Equal(t, uint64(2), result[2].PrepaidId)
}
