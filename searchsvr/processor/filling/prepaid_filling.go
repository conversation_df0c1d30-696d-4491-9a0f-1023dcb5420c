package filling

import (
	"context"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/udp_client"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func PrepaidLandingPageFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaidInfos model.PrepaidInfos) model.PrepaidInfos {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhrasePrepaidFilling, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenFilling, len(prepaidInfos))
	}()
	if len(prepaidInfos) == 0 {
		return prepaidInfos
	}
	// 基本信息填充, 仅需要prepaid 的id，不依赖prepaid 其他信息
	prepaidInfos = PrepaidFillingBaseInfo(ctx, traceInfo, prepaidInfos)

	// 补充信息填充,需要依赖上面的基本信息
	prepaidInfos = PrepaidFillingExtraInfo(ctx, traceInfo, prepaidInfos)

	return prepaidInfos
}

func PrepaidFillingBaseInfo(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaidInfos model.PrepaidInfos) model.PrepaidInfos {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePrepaidFillingBaseInfo, time.Since(pt))
	}()
	var prepaidMetaMap map[uint64]*o2oalgo.Item
	var prepaidUserHistoryData map[uint64]uint64
	var storeMetaMap map[uint64]*o2oalgo.Store
	var dishMetaMap map[uint64]*o2oalgo.Dish
	var prepaidFeatureMap map[uint64]map[string]interface{}
	var userBrandPurchase map[uint64]uint64
	var err1 error
	wg := sync.WaitGroup{}
	wg.Add(1)
	goroutine.WithGo(ctx, "PrepaidMetaFilling", func(params ...interface{}) {
		defer wg.Done()
		prepaidMetaMap = batchGetPrepaidMeta(ctx, traceInfo, prepaidInfos.DistinctPrepaidIds())
	})
	wg.Add(1)
	goroutine.WithGo(ctx, "UserHistorySalesVolumeFilling", func(params ...interface{}) {
		defer wg.Done()
		prepaidUserHistoryData, err1 = mlplatform.GetPrepaidUserHistorySalesVolume(ctx, traceInfo.IsDebug, traceInfo.TraceRequest.PublishId, traceInfo.UserId, prepaidInfos.DistinctPrepaidIds())
		if err1 != nil {
			logkit.FromContext(ctx).Error("GetPrepaidUserHistorySalesVolume fail", logkit.Uint64("UserId", traceInfo.UserId), logkit.Any("prepaidIds", prepaidInfos.DistinctPrepaidIds()), logkit.Err(err1))
			prepaidUserHistoryData = make(map[uint64]uint64)
		}
	})
	wg.Add(1)
	goroutine.WithGo(ctx, "StoreMetaFilling", func(params ...interface{}) {
		defer wg.Done()
		storeMetaMap = batchGetStoreMeta(ctx, traceInfo, prepaidInfos.DistinctStoreIds())
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "DishMetaFilling", func(params ...interface{}) {
		defer wg.Done()
		dishMetaMap = batchGetDishMeta(ctx, traceInfo, prepaidInfos.DistinctDishIds())
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "PrepaidFeaturesFilling", func(params ...interface{}) {
		defer wg.Done()
		prepaidFeatureMap = batchGetPrepaidFeatures(ctx, traceInfo, prepaidInfos.DistinctPrepaidIds())
	})
	wg.Add(1)
	goroutine.WithGo(ctx, "UserBrandPurchaseFilling", func(params ...interface{}) {
		defer wg.Done()
		userBrandPurchase = batchGetUserBrandPurchase(ctx, traceInfo, traceInfo.UserId)
	})

	wg.Wait()

	res := make([]*model.PrepaidInfo, 0, len(prepaidInfos))
	lng := traceInfo.TraceRequest.Longitude
	lat := traceInfo.TraceRequest.Latitude
	for _, prepaidInfo := range prepaidInfos {
		prepaidMeta, exist := prepaidMetaMap[prepaidInfo.PrepaidId]
		if exist == false || prepaidMeta == nil || prepaidMeta.GetId() == 0 {
			traceInfo.AddFilteredStore(prepaidInfo.PrepaidId, traceinfo.FilteredTypeWithoutStoreMeta, prepaidInfo)
			continue
		}
		prepaidInfo.InitFeatureParams()
		prepaidInfo.PrepaidMeta = prepaidMeta
		fillingQuotaAndSoldOut(prepaidMeta, prepaidInfo)
		if sales, ok := prepaidUserHistoryData[prepaidInfo.PrepaidId]; ok {
			prepaidInfo.UserHistorySalesVolume = sales
		}
		// data server feature, 从配置获取，当前已有的属性：
		// prepaid_sku_ctr
		// prepaid_sku_cvr
		// prepaid_item_sold_count
		// i_dish_ctr
		// i_dish_cvr
		if _, ok := prepaidFeatureMap[prepaidInfo.PrepaidId]; ok {
			for featureName, value := range prepaidFeatureMap[prepaidInfo.PrepaidId] {
				prepaidInfo.FeatureParams[featureName] = value // 已经内部处理过默认值
			}
		}
		if storeMeta, ok := storeMetaMap[prepaidInfo.StoreId]; ok {
			prepaidInfo.StoreMeta = storeMeta
			prepaidInfo.BrandId = storeMeta.GetBrandId()
			prepaidInfo.MerchantId = storeMeta.GetMerchantId()
			// distance
			prepaidInfo.Distance = util.CountDistance(lng, lat, storeMeta.GetLocation().GetLongitude64(), storeMeta.GetLocation().GetLatitude64())

			//user brand purchase
			if purchaseCount, existBrand := userBrandPurchase[storeMeta.GetBrandId()]; existBrand {
				prepaidInfo.FeatureParams["user_brand_purchase"] = float64(purchaseCount)
			}
		}
		if dishMeta, ok := dishMetaMap[prepaidInfo.DishId]; ok {
			prepaidInfo.DishMeta = dishMeta
		}

		fillingPrepaidDiscountForLandingPage(prepaidInfo, prepaidMeta)
		prepaidInfo.FeatureParams["discount_score"] = prepaidInfo.PrepaidDiscount

		res = append(res, prepaidInfo)
	}
	return res
}

func PrepaidFillingExtraInfo(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaidInfos model.PrepaidInfos) model.PrepaidInfos {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePrepaidFillingExtraInfo, time.Since(pt))
	}()
	var distanceScoreMap, esScoreMap, saleScoreMap map[uint64]float64

	wg := sync.WaitGroup{}
	wg.Add(1)
	goroutine.WithGo(ctx, "UserGroupFilling", func(params ...interface{}) {
		defer wg.Done()
		traceInfo.UserGroupMap = getUdpGroups(ctx, traceInfo, prepaidInfos) // 依赖PrepaidName
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "DistanceScoreFilling", func(params ...interface{}) {
		defer wg.Done()
		distanceScoreMap = CountDistanceScores(ctx, traceInfo, prepaidInfos) // 依赖Distance
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "ESScoreFilling", func(params ...interface{}) {
		defer wg.Done()
		esScoreMap = CountESScores(ctx, traceInfo, prepaidInfos) // 依赖Distance
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "SaleScoreFilling", func(params ...interface{}) {
		defer wg.Done()
		saleScoreMap = CountSaleScores(ctx, traceInfo, prepaidInfos) // 依赖Distance
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "PrepaidStorePolygonFilling", func(params ...interface{}) {
		defer wg.Done()
		PrepaidPolygonFillingBatch(ctx, traceInfo, prepaidInfos) // 依赖门店正排
	})

	wg.Wait()

	for _, prepaidInfo := range prepaidInfos {
		if distanceScore, ok := distanceScoreMap[prepaidInfo.PrepaidId]; ok {
			prepaidInfo.FeatureParams["dist_score"] = distanceScore
		}
		if relScore, ok := esScoreMap[prepaidInfo.PrepaidId]; ok {
			prepaidInfo.FeatureParams["rel_score"] = relScore
		}
		if saleScore, ok := saleScoreMap[prepaidInfo.PrepaidId]; ok {
			prepaidInfo.FeatureParams["sale_score"] = saleScore
		}
	}

	return prepaidInfos
}

func PrepaidForBagFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaidInfos model.PrepaidInfos) model.PrepaidInfos {
	res := make([]*model.PrepaidInfo, 0, len(prepaidInfos))
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhrasePrepaidFilling, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenFilling, len(prepaidInfos))
	}()

	var prepaidMetaMap map[uint64]*o2oalgo.Item
	var prepaidSkuHistorySoldData map[uint64]uint64
	var err1 error
	wg := sync.WaitGroup{}
	wg.Add(1)
	goroutine.WithGo(ctx, "PrepaidMetaFilling", func(params ...interface{}) {
		defer wg.Done()
		prepaidMetaMap = batchGetPrepaidMeta(ctx, traceInfo, prepaidInfos.DistinctPrepaidIds())
	})
	wg.Add(1)
	goroutine.WithGo(ctx, "PrepaidSkuHistorySoldDataFilling", func(params ...interface{}) {
		defer wg.Done()
		prepaidSkuHistorySoldData, err1 = mlplatform.GetPrepaidYearSalesVolume(ctx, traceInfo.IsDebug, traceInfo.TraceRequest.PublishId, prepaidInfos.DistinctPrepaidIds())
		if err1 != nil {
			logkit.FromContext(ctx).Error("GetPrepaidYearSalesVolume fail", logkit.Any("prepaidIds", prepaidInfos.DistinctPrepaidIds()), logkit.Err(err1))
			prepaidSkuHistorySoldData = make(map[uint64]uint64)
		}
	})
	wg.Wait()
	queryLower := strings.TrimSpace(strings.ToLower(util.RemoveExtraSpace(traceInfo.QueryKeyword)))
	for _, prepaidInfo := range prepaidInfos {
		prepaidMeta, exist := prepaidMetaMap[prepaidInfo.PrepaidId]
		if exist == false || prepaidMeta == nil || prepaidMeta.GetId() == 0 {
			traceInfo.AddFilteredStore(prepaidInfo.PrepaidId, traceinfo.FilteredTypeWithoutStoreMeta, prepaidInfo)
			continue
		}
		prepaidInfo.InitFeatureParams()
		prepaidInfo.PrepaidMeta = prepaidMeta
		fillingQuotaAndSoldOut(prepaidMeta, prepaidInfo)
		fillingPrepaidDiscountForBag(prepaidInfo, prepaidMeta)
		fillingExactMatch(prepaidInfo, queryLower, prepaidMeta)

		// prepaid 一年销量统计
		if sales, ok := prepaidSkuHistorySoldData[prepaidInfo.PrepaidId]; ok {
			prepaidInfo.PrepaidYearSales = sales
		}

		res = append(res, prepaidInfo)
	}
	return prepaidInfos
}

func fillingExactMatch(prepaidInfo *model.PrepaidInfo, queryLower string, prepaidMeta *o2oalgo.Item) {
	prepaidIdStr := strconv.FormatUint(prepaidInfo.PrepaidId, 10)
	if prepaidIdStr == queryLower {
		prepaidInfo.ExactMatch = 1
		return
	}
	for _, itemTitle := range prepaidMeta.GetItemTitle() {
		title := strings.TrimSpace(strings.ToLower(util.RemoveExtraSpace(itemTitle)))
		if title == queryLower {
			prepaidInfo.ExactMatch = 1
			return
		}
	}
}

// 折扣：(item_original_price-prepaid_purchase_price) / item_original_price。  landing page 和 for bag 两边计算折扣时，用到的菜品价格不一样。
// landing page有绑定菜品，item_original_price 是直接从菜品正排获取菜品的价格参与计算
func fillingPrepaidDiscountForLandingPage(prepaidInfo *model.PrepaidInfo, prepaidMeta *o2oalgo.Item) {
	if prepaidInfo == nil || prepaidMeta == nil || prepaidInfo.DishMeta == nil || prepaidInfo.DishMeta.GetPrice() == 0 {
		return
	}
	// VN特殊处理，存库的时候，商品*100000，菜品*100
	if cid.IsVN() {
		prepaidInfo.PrepaidDiscount = 1 - (float64(prepaidMeta.GetListPrice())/100000)/(float64(prepaidInfo.DishMeta.GetPrice())/100)
	} else {
		prepaidInfo.PrepaidDiscount = 1 - float64(prepaidMeta.GetListPrice())/float64(prepaidInfo.DishMeta.GetPrice())
	}
}

// 折扣：(item_original_price-prepaid_purchase_price) / item_original_price。  landing page 和 for bag 两边计算折扣时，用到的菜品价格不一样。
// for bag 没有绑定菜品，item_original_price 是直接从所有菜品的最大价格参与计算，从ES正排返回
func fillingPrepaidDiscountForBag(prepaidInfo *model.PrepaidInfo, prepaidMeta *o2oalgo.Item) {
	if prepaidInfo == nil || prepaidMeta == nil || prepaidInfo.MaxDishPrice == 0 {
		return
	}
	// VN特殊处理，存库的时候，商品*100000，菜品*100
	if cid.IsVN() {
		prepaidInfo.PrepaidDiscount = 1 - (float64(prepaidMeta.GetListPrice())/100000)/(float64(prepaidInfo.MaxDishPrice)/100)
	} else {
		prepaidInfo.PrepaidDiscount = 1 - float64(prepaidMeta.GetListPrice())/float64(prepaidInfo.MaxDishPrice)
	}
}

func fillingQuotaAndSoldOut(prepaidMeta *o2oalgo.Item, prepaidInfo *model.PrepaidInfo) {
	quota := uint64(0)
	if len(prepaidMeta.GetItemQuota()) > 0 {
		for _, iq := range prepaidMeta.ItemQuota {
			quota = iq.GetQuotaValue()
			if quota > 0 {
				break
			}
		}
	}
	prepaidInfo.ItemQuota = quota
	if prepaidInfo.ItemQuota <= prepaidMeta.GetSalesVolume() {
		prepaidInfo.IsSoldOut = 1
	}
}

func getUdpGroups(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) map[string]struct{} {
	// 1.通过user group获取用户所在的城市信息
	groupMap := make(map[string]struct{}, len(prepaids))
	groupNames := make([]string, 0, len(prepaids))
	tmpMap := make(map[string]struct{}, len(prepaids))
	for _, prepaid := range prepaids {
		groupName := prepaid.PrepaidMeta.GetApplicableUserGroupName()
		if len(groupName) > 0 {
			if _, ok := tmpMap[groupName]; !ok {
				groupNames = append(groupNames, groupName)
				tmpMap[groupName] = struct{}{}
			}

		}
	}
	if len(groupNames) == 0 {
		return groupMap
	}
	t1 := time.Now()
	uGroupNames, err := udp_client.GetUdpClient().GetUdGroups(ctx, traceInfo.UserId, groupNames)
	metric_reporter2.ReportDurationAndQPS(time.Since(t1), metric_reporter2.SearchReportTypeRpc, "", "", "GetUdGroups")
	if err != nil {
		logkit.FromContext(ctx).Error("GetUdGroups fail", logkit.Uint64("UserId", traceInfo.UserId), logkit.Any("groupNames", groupNames), logkit.Err(err))
		// 如果请求udb报错，直接不做过滤
		return groupMap
	}

	for _, name := range uGroupNames {
		groupMap[name] = struct{}{}
	}
	return groupMap
}

func CountDistanceScores(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) map[uint64]float64 {
	distBuck := abtest.GetPrepaidDistanceScoreDistanceBucket(ctx, traceInfo.AbParamClient)
	maxDist := abtest.GetPrepaidDistanceScoreMaxDistance(ctx, traceInfo.AbParamClient)

	resMap := make(map[uint64]float64, len(prepaids))
	if distBuck == 0 || maxDist == 0 {
		return resMap
	}
	for i, prepaid := range prepaids {
		resMap[prepaid.PrepaidId] = 1.0 - min(prepaid.Distance/distBuck, maxDist)/maxDist // 归一化到[0,1]之间
		if traceInfo.IsDebug && i < 10 {
			logkit.FromContext(ctx).Info("Prepaid Distance Score",
				logkit.Uint64("PrepaidId", prepaid.PrepaidId),
				logkit.Float64("Distance", prepaid.Distance),
				logkit.Float64("MaxDistance", maxDist),
				logkit.Float64("DistanceBucket", distBuck),
				logkit.Float64("Score", resMap[prepaid.PrepaidId]))
		}
	}
	return resMap
}

func CountESScores(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) map[uint64]float64 {
	minScore := math.MaxFloat64
	maxScore := 0.0
	for _, prepaid := range prepaids {
		if prepaid.ESScore < minScore {
			minScore = prepaid.ESScore
		}
		if prepaid.ESScore > maxScore {
			maxScore = prepaid.ESScore
		}
	}
	resMap := make(map[uint64]float64, len(prepaids))
	delta := maxScore - minScore
	if delta == 0 {
		return resMap
	}
	for _, prepaid := range prepaids {
		resMap[prepaid.PrepaidId] = (prepaid.ESScore - minScore) / delta // 归一化到[0,1]之间
	}
	return resMap
}

func CountSaleScores(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) map[uint64]float64 {
	minCount := uint64(math.MaxUint64)
	maxCount := uint64(0)
	for _, prepaid := range prepaids {
		if count, exist := prepaid.FeatureParams["prepaid_item_sold_count"]; exist {
			prepaid.PrepaidItemSoldCount = uint64(count.(float64))
		}
		if prepaid.PrepaidItemSoldCount < minCount {
			minCount = prepaid.PrepaidItemSoldCount
		}
		if prepaid.PrepaidItemSoldCount > maxCount {
			maxCount = prepaid.PrepaidItemSoldCount
		}
	}
	resMap := make(map[uint64]float64, len(prepaids))
	delta := float64(maxCount - minCount)
	if delta == 0 {
		return resMap
	}
	for _, prepaid := range prepaids {
		resMap[prepaid.PrepaidId] = float64(prepaid.PrepaidItemSoldCount-minCount) / delta // 归一化到[0,1]之间
	}
	return resMap
}
