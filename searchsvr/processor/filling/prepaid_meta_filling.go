package filling

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"go.uber.org/zap"
)

func batchGetPrepaidMeta(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaidIds []uint64) map[uint64]*o2oalgo.Item {
	nPrepaid := len(prepaidIds)
	if nPrepaid == 0 {
		return map[uint64]*o2oalgo.Item{}
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingPrepaidMeta, time.Since(pt))
	}()
	itemMeta := make(map[uint64]*o2oalgo.Item)
	t1 := time.Now()
	rsp, err, partFailedFlag := integrate.DataManageServiceClient.GetPrepaidSKU(ctx, prepaidIds)
	logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetItem"), zap.String("cost", time.Since(t1).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(t1), metric_reporter2.SearchReportTypeRpc, "", "", "GetPrepaidMeta")
	if partFailedFlag {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_META_ERROR)
	}
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("DataManageServiceClient.GetItem error")
		return itemMeta
	}
	itemMeta = rsp.GetMapItem()
	return itemMeta
}
