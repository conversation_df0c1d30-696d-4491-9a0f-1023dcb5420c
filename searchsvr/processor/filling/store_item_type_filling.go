package filling

import (
	"context"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StoreItemTypeFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo, itemType foodalgo_search.ItemSceneType) []*model.StoreInfo {
	for _, store := range stores {
		store.ItemSceneType = itemType
	}
	return stores
}

func PrepaidItemTypeFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids []*model.PrepaidInfo, itemType foodalgo_search.ItemSceneType) []*model.PrepaidInfo {
	for _, prepaid := range prepaids {
		prepaid.ItemSceneType = itemType
	}
	return prepaids
}
