package filling

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/data_server"
)

func batchGetPrepaidFeatures(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaidIds []uint64) map[uint64]map[string]interface{} {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingPrepaidFeatures, time.Since(pt))
	}()
	// 查询配置
	factorConfigsMap := abtest.GetDataServerFeatureConfigs(ctx, traceInfo.AbParamClient)
	result := make(map[uint64]map[string]interface{}, len(factorConfigsMap)) // prepaidId -> factorName -> value
	var defaultValue, dataValue float64
	for bid, featureIdConfigMap := range factorConfigsMap {
		if len(featureIdConfigMap) == 0 {
			logkit.FromContext(ctx).Info("factorConfigsMap is empty", logkit.Uint64("bid", bid))
			continue
		}
		featureIds := make([]uint64, 0, len(featureIdConfigMap))
		keyType := constants.KeyTypePrepaidId
		for featureId, config := range featureIdConfigMap {
			featureIds = append(featureIds, featureId)
			if config.KeyType == constants.KeyTypeQueryPrepaidId {
				keyType = constants.KeyTypeQueryPrepaidId
			}
			// 默认值
			if config.DataKind == constants.DataKindInt {
				if config.DefaultValue != "" {
					if val, err := strconv.ParseInt(config.DefaultValue, 10, 64); err == nil {
						defaultValue = float64(val)
					} else {
						logkit.FromContext(ctx).Error("parse default value error", logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId), logkit.String("defaultValue", config.DefaultValue), logkit.Err(err))
					}
				} else {
					// 如果没有默认值，使用0
					defaultValue = 0.0
				}
			} else {
				// 默认值为浮点数
				if config.DefaultValue != "" {
					if val, err := strconv.ParseFloat(config.DefaultValue, 64); err == nil {
						defaultValue = val
					} else {
						logkit.FromContext(ctx).Error("parse default value error", logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId), logkit.String("defaultValue", config.DefaultValue), logkit.Err(err))
					}
				} else {
					// 如果没有默认值，使用0.0
					defaultValue = 0.0
				}
			}
			for _, prepaidId := range prepaidIds {
				if result[prepaidId] == nil {
					result[prepaidId] = make(map[string]interface{})
				}
				result[prepaidId][config.FactorName] = defaultValue
			}
		}
		keyList := make([]string, 0, len(prepaidIds))
		if keyType == constants.KeyTypeQueryPrepaidId {
			query := strings.TrimSpace(strings.ToLower(traceInfo.QueryKeyword))
			for _, prepaidId := range prepaidIds {
				if prepaidId == 0 {
					continue
				}
				keyList = append(keyList, fmt.Sprintf("%s_%d", query, prepaidId))
			}
		} else {
			for _, prepaidId := range prepaidIds {
				if prepaidId == 0 {
					continue
				}
				keyList = append(keyList, strconv.Itoa(int(prepaidId)))
			}
		}
		dataMap := data_server.GetFromDataServer(ctx, traceInfo, keyList, bid, featureIds)
		if len(dataMap) == 0 {
			logkit.FromContext(ctx).Error("dataMap is empty", logkit.Uint64("bid", bid))
			continue
		}
		for prepaidIdStr, featureData := range dataMap {
			prepaidId, _ := strconv.ParseUint(prepaidIdStr, 10, 64)
			for featureId, data := range featureData {
				if data == nil {
					logkit.FromContext(ctx).Error("data is nil", logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId), logkit.String("prepaidId", prepaidIdStr))
					continue
				}
				config := featureIdConfigMap[featureId]
				if config.DataKind == constants.DataKindFloat && len(data.GetFloatList().GetValue()) == 1 {
					dataValue = float64(data.GetFloatList().GetValue()[0])
				} else if config.DataKind == constants.DataKindInt && len(data.GetInt64List().GetValue()) == 1 {
					dataValue = float64(data.GetInt64List().GetValue()[0])
				} else {
					logkit.FromContext(ctx).Error("data kind mismatch", logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId),
						logkit.String("dataKind", config.DataKind), logkit.String("prepaidId", prepaidIdStr), logkit.Any("data", data))
					continue
				}
				if _, ok := result[prepaidId]; !ok {
					result[prepaidId] = make(map[string]interface{})
				}
				factorName := factorConfigsMap[bid][featureId].FactorName
				if factorName == "" {
					logkit.FromContext(ctx).Error("factorName is empty", logkit.Uint64("bid", bid), logkit.Uint64("featureId", featureId))
					continue
				}
				result[prepaidId][factorName] = dataValue
			}
		}
	}
	return result
}

func batchGetUserBrandPurchase(ctx context.Context, traceInfo *traceinfo.TraceInfo, userId uint64) map[uint64]uint64 {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePrepaidFillingUserBrandPurchase, time.Since(pt))
	}()
	result := make(map[uint64]uint64) // brandId -> purchaseCount
	if userId == 0 {
		logkit.FromContext(ctx).Info("userId is zero, skip batchGetUserBrandPurchase")
		return result
	}
	userIdStr := strconv.FormatUint(userId, 10)
	dataMap := data_server.GetFromDataServer(ctx, traceInfo, []string{userIdStr}, constants.DataBid_SQSUserFeature, []uint64{constants.DataBid_SQSUserFeature_Brand, constants.DataBid_SQSUserFeature_PlacedOrders})
	if len(dataMap) == 0 || len(dataMap[userIdStr]) == 0 {
		logkit.FromContext(ctx).Info("userId brand data is empty")
		return result
	}
	if _, ok := dataMap[userIdStr][constants.DataBid_SQSUserFeature_Brand]; !ok {
		logkit.FromContext(ctx).Info("userId brand data is missing", logkit.Uint64("userId", userId), logkit.Any("dataMap", dataMap))
		return result
	}
	if len(dataMap[userIdStr][constants.DataBid_SQSUserFeature_Brand].GetBytesList().GetValue()) != 1 {
		logkit.FromContext(ctx).Error("userId brand data length mismatch", logkit.Uint64("userId", userId), logkit.Any("dataMap", dataMap))
		return result
	}
	brandIdListStr := dataMap[userIdStr][constants.DataBid_SQSUserFeature_Brand].GetBytesList().GetValue()[0]
	purchaseCountListStr := dataMap[userIdStr][constants.DataBid_SQSUserFeature_PlacedOrders].GetBytesList().GetValue()[0]
	brandIdList := strings.Split(string(brandIdListStr), ",")
	purchaseCountList := strings.Split(string(purchaseCountListStr), ",")
	if len(brandIdList) != len(purchaseCountList) {
		logkit.FromContext(ctx).Error("brandIds and purchaseCount length mismatch",
			logkit.Uint64("userId", userId),
			logkit.Int("brandIdList len", len(brandIdList)),
			logkit.Int("purchaseCountList len", len(purchaseCountList)))
		logkit.Any("dataMap", dataMap)
	}
	for i, brandIdStr := range brandIdList {
		brandId, err1 := strconv.ParseUint(brandIdStr, 10, 64)
		if err1 != nil {
			logkit.FromContext(ctx).Error("parse brandId error", logkit.Uint64("userId", userId), logkit.String("brandId", brandIdStr), logkit.Err(err1))
			continue
		}
		purchaseCount, err2 := strconv.ParseUint(purchaseCountList[i], 10, 64)
		if err2 != nil {
			logkit.FromContext(ctx).Error("parse purchaseCount error", logkit.Uint64("userId", userId), logkit.String("purchaseCount", purchaseCountList[i]), logkit.Err(err2))
			continue
		}
		result[brandId] = purchaseCount
	}
	return result
}
