package filling

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/polygon"
)

func StorePolygonFillingBatch(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStorePolygon, time.Since(pt))
	}()
	nStore := len(stores)
	var keyList = make([]string, 0, nStore)
	for _, store := range stores {
		// 带 self pickup filter时，直接使用自提距离过滤
		if (env.GetCID() == cid.TH || env.GetCID() == cid.MY) && decision.IsSelfPickupFilter(traceInfo) && store.SelfPickupDistance > 0 {
			keyList = append(keyList, fmt.Sprintf("%d_%d", store.StoreId, store.SelfPickupDistance))
		} else {
			keyList = append(keyList, fmt.Sprintf("%d_%d", store.StoreId, store.DeliveryDistance))
			if store.IsSelfPickupRecall() && store.SelfPickupDistance != store.DeliveryDistance {
				keyList = append(keyList, fmt.Sprintf("%d_%d", store.StoreId, store.SelfPickupDistance))
			}
		}
	}
	polygonMap := batchGetPolygon(ctx, traceInfo, nStore, keyList)
	for _, store := range stores {
		polygonKey := fmt.Sprintf("%d_%d", store.StoreId, store.DeliveryDistance)
		store.StorePolygon = polygonMap[polygonKey]

		polygonPickupKey := fmt.Sprintf("%d_%d", store.StoreId, store.SelfPickupDistance)
		store.StorePickupPolygon = polygonMap[polygonPickupKey]
	}
}

func batchGetPolygon(ctx context.Context, traceInfo *traceinfo.TraceInfo, nStore int, keyList []string) map[string]*polygon.Polygon {
	batchSize := 50
	if apollo.SearchApolloCfg.PolygonMGetBatchSize != 0 {
		batchSize = apollo.SearchApolloCfg.PolygonMGetBatchSize
	}
	total := nStore / batchSize
	if nStore%batchSize > 0 {
		total += 1
	}
	var polygonMapGroup = make([]map[string]*polygon.Polygon, total)
	wg := sync.WaitGroup{}
	for i := 0; i < total; i++ {
		var tempKeys []string
		if i+1 < total {
			tempKeys = keyList[i*batchSize : (i+1)*batchSize]
		} else {
			tempKeys = keyList[i*batchSize:]
		}
		wg.Add(1)
		goroutine.WithGo(ctx, "MGetStorePolygon", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			keys := param[1].([]string)
			pm := make(map[string]*polygon.Polygon, len(keys))
			for _, k := range keys {
				pm[k] = nil
			}
			getStorePolygonStartTime := time.Now()
			err := localcache.StoreCacheSysInstance.MGetStorePolygon(ctx, pm)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "MGetStorePolygon"), zap.String("cost", time.Since(getStorePolygonStartTime).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(getStorePolygonStartTime), metric_reporter2.SearchReportTypeRpc, "", "", "MGetStorePolygon")
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_META_ERROR)
			}
			polygonMapGroup[index] = pm
		}, i, tempKeys)
	}
	wg.Wait()
	var polygonMap = make(map[string]*polygon.Polygon, nStore)
	for _, pm := range polygonMapGroup {
		for key, val := range pm {
			if val != nil {
				polygonMap[key] = val
			}
		}
	}
	return polygonMap
}

func PrepaidPolygonFillingBatch(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStorePolygon, time.Since(pt))
	}()
	nStore := len(prepaids)
	var keyList = make([]string, 0, nStore)
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.StoreMeta == nil || prepaid.StoreMeta.GetDeliveryDistance() <= 0 {
			continue
		}
		prepaid.PolygonKey = fmt.Sprintf("%d_%d", prepaid.StoreMeta.GetId(), prepaid.StoreMeta.GetDeliveryDistance())
		keyList = append(keyList, prepaid.PolygonKey)
	}
	polygonMap := batchGetPolygon(ctx, traceInfo, nStore, keyList)
	for _, prepaid := range prepaids {
		if prepaid == nil || prepaid.StoreMeta == nil || prepaid.StoreMeta.GetDeliveryDistance() <= 0 {
			continue
		}
		if pol, exist := polygonMap[prepaid.PolygonKey]; exist {
			prepaid.StorePolygon = pol
		}
	}
}
