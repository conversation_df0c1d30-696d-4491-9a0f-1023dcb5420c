package rerank

import (
	"context"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func PrepaidReRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaids model.PrepaidInfos) model.PrepaidInfos {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhrasePrepaidReRank, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenReRank, len(prepaids))
	}()
	prepaids = BrandShufflePrepaids(ctx, traceInfo, prepaids)
	return prepaids
}

// 判断 candidate 是否可以放在 result 的末尾（根据前两个判断）
func isValid(result []*model.PrepaidInfo, candidate *model.PrepaidInfo) bool {
	n := len(result)
	// 如果 result 为空，直接满足条件
	if n == 0 {
		return true
	}
	last1 := result[n-1]
	if n == 1 {
		return isValidOne(last1, candidate)
	}
	// 至少有两项时，获取最后两项,两个同时满足才可以
	last2 := result[n-2]
	return isValidOne(last1, candidate) && isValidOne(last2, candidate)
}

// 判断 candidate 跟 last 是否可以放一块
func isValidOne(last, candidate *model.PrepaidInfo) bool {
	// 品牌都不为 0，则品牌不同可以放一起
	if last.BrandId != 0 && candidate.BrandId != 0 {
		return last.BrandId != candidate.BrandId
	}

	// 品牌有一个为 0，进入 merchantId 检查
	if last.MerchantId != 0 && candidate.MerchantId != 0 {
		// merchantId 相同，不可以放一起
		return last.MerchantId != candidate.MerchantId
	}

	// merchantId 有一个为 0 的情况
	return last.StoreId != candidate.StoreId
}

// 主函数：将冲突项向后移动，并保持顺序稳定
func BrandShufflePrepaids(ctx context.Context, traceInfo *traceinfo.TraceInfo, prepaidInfos []*model.PrepaidInfo) []*model.PrepaidInfo {
	if abtest.GetSkipPrepaidBrandShuffle(ctx, traceInfo.AbParamClient) {
		// 如果配置了跳过品牌打散，直接返回原始列表
		return prepaidInfos
	}
	if traceInfo.QPResult != nil && len(traceInfo.QPResult.QueryStoreIntention) > 0 {
		// 如果明确是门店意图时（c_query_store_intention == 1），需要跳过品牌打散.直接返回原始列表
		return prepaidInfos
	}
	n := len(prepaidInfos)
	if n <= 2 {
		return prepaidInfos
	}
	// 记录打散前位置
	for i, prepaidInfo := range prepaidInfos {
		prepaidInfo.PosBeforeBrandShuffle = int64(i + 1) // 1-based index
	}
	result := make([]*model.PrepaidInfo, 0, n)
	used := make([]bool, n)
	firstUnused := 0 // 始终指向第一个未使用的下标
	for len(result) < n {
		// 如果前10个已完成，直接把剩下的全部 append
		if len(result) >= 10 {
			for i := firstUnused; i < n; i++ {
				if !used[i] {
					result = append(result, prepaidInfos[i])
					used[i] = true
				}
			}
			break
		}
		inserted := false
		for i := firstUnused; i < n; i++ {
			if used[i] {
				continue
			}
			if isValid(result, prepaidInfos[i]) {
				result = append(result, prepaidInfos[i])
				used[i] = true
				inserted = true
				// 更新 firstUnused
				for firstUnused < n && used[firstUnused] {
					firstUnused++
				}
				break
			}
		}
		// 所有未插入项都冲突，强插 firstUnused 元素
		if !inserted && firstUnused < n && !used[firstUnused] {
			result = append(result, prepaidInfos[firstUnused])
			used[firstUnused] = true
			for firstUnused < n && used[firstUnused] {
				firstUnused++
			}
		}
	}
	// 记录打散后位置
	for i, prepaidInfo := range result {
		prepaidInfo.PosAfterBrandShuffle = int64(i + 1) // 1-based index
	}
	return result
}
