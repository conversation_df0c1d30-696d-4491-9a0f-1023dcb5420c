package rerank

import (
	"context"
	"fmt"
	"testing"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/stretchr/testify/assert"
)

// 检查前10项是否打散成功
func isTop10Valid(result []*model.PrepaidInfo) bool {
	n := len(result)
	limit := 10
	if n < 10 {
		limit = n
	}

	for i := 0; i < limit; i++ {
		for j := i - 2; j < i; j++ {
			if j < 0 {
				continue
			}
			if result[i].BrandId != 0 && result[i].BrandId == result[j].BrandId {
				return false
			}
			if result[i].BrandId == 0 && result[j].BrandId == 0 &&
				result[i].MerchantId == result[j].MerchantId {
				return false
			}
		}
	}
	return true
}

func TestShuffle_Basic(t *testing.T) {
	input := []*model.PrepaidInfo{
		{PrepaidId: 1, BrandId: 100, MerchantId: 10},
		{PrepaidId: 2, BrandId: 100, MerchantId: 10},
		{PrepaidId: 3, BrandId: 100, MerchantId: 10},
		{PrepaidId: 4, BrandId: 200, MerchantId: 20},
		{PrepaidId: 5, BrandId: 200, MerchantId: 20},
		{PrepaidId: 6, BrandId: 0, MerchantId: 30},
		{PrepaidId: 7, BrandId: 0, MerchantId: 30},
		{PrepaidId: 8, BrandId: 0, MerchantId: 30},
		{PrepaidId: 9, BrandId: 300, MerchantId: 40},
		{PrepaidId: 10, BrandId: 400, MerchantId: 50},
		{PrepaidId: 11, BrandId: 100, MerchantId: 10},
		{PrepaidId: 12, BrandId: 600, MerchantId: 60},
	}

	output := BrandShufflePrepaids(context.Background(), &traceinfo.TraceInfo{}, input)

	if len(output) != len(input) {
		t.Errorf("Output length mismatch, got %d, want %d", len(output), len(input))
	}

	if !isTop10Valid(output) {
		t.Errorf("Top 10 items do not satisfy the shuffle rule.")
	}
}

func TestShuffle_Batch(t *testing.T) {
	var testCases = [][]*model.PrepaidInfo{
		// 1. 基础重复 brandId，打散冲突，merchantId 不冲突
		{
			{PrepaidId: 1, BrandId: 100, MerchantId: 10},
			{PrepaidId: 2, BrandId: 100, MerchantId: 10},
			{PrepaidId: 3, BrandId: 200, MerchantId: 20},
			{PrepaidId: 4, BrandId: 300, MerchantId: 30},
		},

		// 2. 多个 brandId = 0，merchantId 相同，检测 merchantId 冲突
		{
			{PrepaidId: 5, BrandId: 0, MerchantId: 10},
			{PrepaidId: 6, BrandId: 0, MerchantId: 10},
			{PrepaidId: 7, BrandId: 0, MerchantId: 20},
			{PrepaidId: 8, BrandId: 0, MerchantId: 30},
		},

		// 3. brandId 与 merchantId 混合多样，检测两者打散
		{
			{PrepaidId: 9, BrandId: 100, MerchantId: 10},
			{PrepaidId: 10, BrandId: 0, MerchantId: 10},
			{PrepaidId: 11, BrandId: 100, MerchantId: 20},
			{PrepaidId: 12, BrandId: 0, MerchantId: 20},
		},

		// 4. 多个相同 brandId 连续出现，测试顺序后移稳定性
		{
			{PrepaidId: 13, BrandId: 300, MerchantId: 30},
			{PrepaidId: 14, BrandId: 300, MerchantId: 30},
			{PrepaidId: 15, BrandId: 300, MerchantId: 40},
			{PrepaidId: 16, BrandId: 400, MerchantId: 40},
		},

		// 5. 品牌全为0，merchantId复杂交叉，检测merchant打散
		{
			{PrepaidId: 17, BrandId: 0, MerchantId: 50},
			{PrepaidId: 18, BrandId: 0, MerchantId: 60},
			{PrepaidId: 19, BrandId: 0, MerchantId: 50},
			{PrepaidId: 20, BrandId: 0, MerchantId: 70},
		},

		// 6. brandId 和 merchantId 皆不同，理想无冲突
		{
			{PrepaidId: 21, BrandId: 500, MerchantId: 80},
			{PrepaidId: 22, BrandId: 600, MerchantId: 90},
			{PrepaidId: 23, BrandId: 700, MerchantId: 100},
			{PrepaidId: 24, BrandId: 800, MerchantId: 110},
		},

		// 7. 连续多个 merchantId 相同但 brandId 不同，检测 brand 优先打散
		{
			{PrepaidId: 25, BrandId: 900, MerchantId: 100},
			{PrepaidId: 26, BrandId: 1000, MerchantId: 100},
			{PrepaidId: 27, BrandId: 900, MerchantId: 100},
			{PrepaidId: 28, BrandId: 1000, MerchantId: 200},
		},

		// 8. 前两个 brandId 都为 0，merchantId 相同，检测冲突后移
		{
			{PrepaidId: 29, BrandId: 0, MerchantId: 150},
			{PrepaidId: 30, BrandId: 0, MerchantId: 150},
			{PrepaidId: 31, BrandId: 2000, MerchantId: 160},
			{PrepaidId: 32, BrandId: 2000, MerchantId: 170},
		},

		// 9. brandId 为0和非0交错，确保逻辑正确
		{
			{PrepaidId: 33, BrandId: 0, MerchantId: 180},
			{PrepaidId: 34, BrandId: 2100, MerchantId: 180},
			{PrepaidId: 35, BrandId: 0, MerchantId: 180},
			{PrepaidId: 36, BrandId: 2200, MerchantId: 190},
		},

		// 10. 大量重复 brandId 和 merchantId，测试复杂度和性能
		{
			{PrepaidId: 37, BrandId: 3000, MerchantId: 200},
			{PrepaidId: 38, BrandId: 3000, MerchantId: 200},
			{PrepaidId: 39, BrandId: 0, MerchantId: 200},
			{PrepaidId: 40, BrandId: 3000, MerchantId: 210},
			{PrepaidId: 41, BrandId: 0, MerchantId: 210},
			{PrepaidId: 42, BrandId: 3000, MerchantId: 210},
		},
	}
	for idx, input := range testCases {
		printDeals("🔹 Input:", input)
		output := BrandShufflePrepaids(context.Background(), &traceinfo.TraceInfo{}, input)
		printDeals("✅ Result:", output)
		if len(output) != len(input) {
			t.Errorf("index %d:Output length mismatch, got %d, want %d", idx+1, len(output), len(input))
		}

		if !isTop10Valid(output) {
			t.Errorf("index %d:Top 10 items do not satisfy the shuffle rule.", idx+1)
		} else {
			t.Logf("index %d:Test case passed with %d items, all rules satisfied.", idx+1, len(input))
		}
	}

}

type testCase struct {
	name        string
	input       []*model.PrepaidInfo
	expectedIds []int
}

func TestShufflePDeals(t *testing.T) {
	testCases := []testCase{
		{
			name: "1. 基础 brandId 冲突打散",
			input: []*model.PrepaidInfo{
				{PrepaidId: 1, BrandId: 100, MerchantId: 10},
				{PrepaidId: 2, BrandId: 100, MerchantId: 10},
				{PrepaidId: 3, BrandId: 200, MerchantId: 20},
				{PrepaidId: 4, BrandId: 300, MerchantId: 30},
			},
			expectedIds: []int{1, 3, 4, 2},
		},
		{
			name: "2. 品牌为 0 且 merchantId 冲突",
			input: []*model.PrepaidInfo{
				{PrepaidId: 5, BrandId: 0, MerchantId: 10},
				{PrepaidId: 6, BrandId: 0, MerchantId: 10},
				{PrepaidId: 7, BrandId: 0, MerchantId: 20},
				{PrepaidId: 8, BrandId: 0, MerchantId: 30},
			},
			expectedIds: []int{5, 7, 8, 6},
		},
		{
			name: "3. 混合 brandId / merchantId 打散",
			input: []*model.PrepaidInfo{
				{PrepaidId: 9, BrandId: 100, MerchantId: 10},
				{PrepaidId: 10, BrandId: 0, MerchantId: 10},
				{PrepaidId: 11, BrandId: 100, MerchantId: 20},
				{PrepaidId: 12, BrandId: 0, MerchantId: 20},
			},
			expectedIds: []int{9, 10, 12, 11},
		},
		{
			name: "4. 连续相同 brandId 出现",
			input: []*model.PrepaidInfo{
				{PrepaidId: 13, BrandId: 300, MerchantId: 30},
				{PrepaidId: 14, BrandId: 300, MerchantId: 30},
				{PrepaidId: 15, BrandId: 300, MerchantId: 40},
				{PrepaidId: 16, BrandId: 400, MerchantId: 40},
			},
			expectedIds: []int{13, 16, 14, 15},
		},
		{
			name: "5. 品牌为 0，merchantId 交叉打散",
			input: []*model.PrepaidInfo{
				{PrepaidId: 17, BrandId: 0, MerchantId: 50},
				{PrepaidId: 18, BrandId: 0, MerchantId: 60},
				{PrepaidId: 19, BrandId: 0, MerchantId: 50},
				{PrepaidId: 20, BrandId: 0, MerchantId: 70},
			},
			expectedIds: []int{17, 18, 20, 19},
		},
		{
			name: "6. 完全无冲突",
			input: []*model.PrepaidInfo{
				{PrepaidId: 21, BrandId: 500, MerchantId: 80},
				{PrepaidId: 22, BrandId: 600, MerchantId: 90},
				{PrepaidId: 23, BrandId: 700, MerchantId: 100},
				{PrepaidId: 24, BrandId: 800, MerchantId: 110},
			},
			expectedIds: []int{21, 22, 23, 24},
		},
		{
			name: "7. 相同 merchantId，brandId 打散",
			input: []*model.PrepaidInfo{
				{PrepaidId: 25, BrandId: 900, MerchantId: 100},
				{PrepaidId: 26, BrandId: 1000, MerchantId: 100},
				{PrepaidId: 27, BrandId: 900, MerchantId: 100},
				{PrepaidId: 28, BrandId: 1000, MerchantId: 200},
			},
			expectedIds: []int{25, 26, 27, 28},
		},
		{
			name: "8. 品牌为 0 merchantId 相同冲突",
			input: []*model.PrepaidInfo{
				{PrepaidId: 29, BrandId: 0, MerchantId: 150},
				{PrepaidId: 30, BrandId: 0, MerchantId: 150},
				{PrepaidId: 31, BrandId: 2000, MerchantId: 160},
				{PrepaidId: 32, BrandId: 2000, MerchantId: 170},
			},
			expectedIds: []int{29, 31, 30, 32},
		},
		{
			name: "9. 品牌为0和非0交错",
			input: []*model.PrepaidInfo{
				{PrepaidId: 33, BrandId: 0, MerchantId: 180},
				{PrepaidId: 34, BrandId: 2100, MerchantId: 180},
				{PrepaidId: 35, BrandId: 0, MerchantId: 180},
				{PrepaidId: 36, BrandId: 2200, MerchantId: 190},
			},
			expectedIds: []int{33, 34, 36, 35},
		},
		{
			name: "10. 大量 brandId 与 merchantId 冲突",
			input: []*model.PrepaidInfo{
				{PrepaidId: 37, BrandId: 3000, MerchantId: 200},
				{PrepaidId: 38, BrandId: 3000, MerchantId: 200},
				{PrepaidId: 39, BrandId: 0, MerchantId: 200},
				{PrepaidId: 40, BrandId: 3000, MerchantId: 210},
				{PrepaidId: 41, BrandId: 0, MerchantId: 210},
				{PrepaidId: 42, BrandId: 3000, MerchantId: 210},
			},
			expectedIds: []int{37, 39, 41, 38, 40, 42},
		},
	}

	for _, c := range testCases {
		t.Run(c.name, func(t *testing.T) {
			output := BrandShufflePrepaids(context.Background(), &traceinfo.TraceInfo{}, c.input)

			var actualIds []int
			for _, p := range output {
				actualIds = append(actualIds, int(p.PrepaidId))
			}

			//t.Logf("Input:")
			//for _, p := range c.input {
			//	t.Logf("  PrepaidId: %d, BrandId: %d, MerchantId: %d", p.PrepaidId, p.BrandId, p.MerchantId)
			//}
			//
			//t.Logf("Output:")
			//for _, p := range output {
			//	t.Logf("  PrepaidId: %d, BrandId: %d, MerchantId: %d", p.PrepaidId, p.BrandId, p.MerchantId)
			//}

			assert.Equal(t, c.expectedIds, actualIds)
		})
	}
}

// 工具函数：打印 PrepaidInfo 列表
func printDeals(title string, deals []*model.PrepaidInfo) {
	fmt.Println(title)
	for _, d := range deals {
		fmt.Printf("PrepaidId: %d, BrandId: %d, MerchantId: %d\n", d.PrepaidId, d.BrandId, d.MerchantId)
	}
	fmt.Println()
}
