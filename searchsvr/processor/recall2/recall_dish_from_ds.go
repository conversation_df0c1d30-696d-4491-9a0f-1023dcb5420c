package recall2

import (
	"context"
	"fmt"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_dishsearcher"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
	"go.uber.org/zap"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func recallDishFromDishSearcher(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) (model.DishInfos, error) {
	if !decision.IsNeedDishSearcherDowngrade(traceInfo) {
		return nil, nil
	}
	if len(storeIds) == 0 {
		return nil, nil
	}

	pt := time.Now()
	var dishInfos model.DishInfos
	defer func() {
		if e := recover(); e != nil {
			logkit.Error("recallDishFromDS panic", logkit.String("recall type", "recallDishFromDS"), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
			reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
				Key: "method",
				Val: "search-dish-recallDishFromDS",
			})
		}

		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingMultiRecallDS, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenRecallFromDS, len(dishInfos))
	}()

	dishInfos, _ = DishSearchDowngrade(ctx, traceInfo, storeIds)
	// 添加dish meta信息
	for _, dish := range dishInfos {
		dish.IsFromDS = true
		dish.RecallPriority = 1 // 平替es，相关性的召回优先级更高
		dish.DishRecallPriorities = []int{1}
		dish.DishRecallType = recallconstant.HardcodeRecallTypeDishSearcherDowngrade
		dish.DishRecallTypes = []string{recallconstant.HardcodeRecallTypeDishSearcherDowngrade}
		dish.DishRecallId = recallconstant.HardcodeRecallIdDishSearcherDowngrade
		dish.DishRecallIds = []string{recallconstant.HardcodeRecallIdDishSearcherDowngrade}
	}

	return dishInfos, nil
}

func DishSearchDowngrade(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) (model.DishInfos, error) {
	pt := time.Now()
	dishList := make(model.DishInfos, 0)
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishRecallDishSearcher, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenRecallFromDS, len(dishList))
	}()
	recallSize := abtest.GetDishListingRecallSize(traceInfo.AbParamClient)

	filter := getDishSearcherFilter()
	sorter := getDishSearcherSorter(recallSize)

	keywordInfos := make([]*o2oalgo_dishsearcher.KeywordInfo, 0, len(traceInfo.QPResult.Segments)*2)
	if cid.IsID() || cid.IsTH() {
		for _, segment := range traceInfo.QPResult.Segments {
			keywordInfos = append(keywordInfos, &o2oalgo_dishsearcher.KeywordInfo{
				Keyword: segment,
				Weight:  1,
			})
		}
	} else {
		tokens := strings.Split(traceInfo.QueryKeyword, " ")
		for _, token := range tokens {
			keywordInfos = append(keywordInfos, &o2oalgo_dishsearcher.KeywordInfo{
				Keyword: token,
				Weight:  1,
			})
		}
	}

	dishInfos := batchDishSearcherSearchItem(ctx, traceInfo, keywordInfos, filter, sorter, storeIds, "add")
	return dishInfos, nil
}

func batchDishSearcherSearchItem(ctx context.Context, traceInfo *traceinfo.TraceInfo, infos []*o2oalgo_dishsearcher.KeywordInfo,
	filters *o2oalgo_dishsearcher.Filter, sorters *o2oalgo_dishsearcher.Sorter,
	storeIds []uint64, calculatedMethodType string) model.DishInfos {

	if traceInfo.IsDebug {
		recallStatName := fmt.Sprintf("%s_%s", "HardcodeRecallDishSearcherDishSearcherDowngrade", recallconstant.HardcodeRecallIdDishSearcherDowngrade)
		traceInfo.AppendRecallsDishFinal(recallStatName)
	}

	batchSize := 150
	if apollo.SearchApolloCfg.NewDishRecallBatch != 0 {
		batchSize = apollo.SearchApolloCfg.NewDishRecallBatch
	}
	total := len(storeIds) / batchSize
	if len(storeIds)%batchSize > 0 {
		total += 1
	}
	dishRecallList := make([]model.DishInfos, total, total)
	wg2 := sync.WaitGroup{}
	wg2.Add(total)
	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.DishSearcherTimeOut
	if timeout == 0 {
		timeout = 5000
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()

	for i := 0; i < total; i++ {
		var tempStoreId []uint64
		if i+1 < total {
			tempStoreId = storeIds[i*batchSize : (i+1)*batchSize]
		} else {
			tempStoreId = storeIds[i*batchSize:]
		}
		goroutine.WithGo(ctx, "DishSearcherSearch", func(params ...interface{}) {
			defer wg2.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			tempIds := param[1].([]uint64)
			request := &o2oalgo_dishsearcher.SearchRequest{
				StoreId:          tempIds,
				KeywordInfo:      infos,
				RecallField:      "",
				Filter:           filters,
				Sorter:           sorters,
				AbTest:           traceInfo.ABTestGroup.GetABTestString(),
				BuyerId:          traceInfo.UserId,
				PublishId:        traceInfo.TraceRequest.PublishId,
				IsDebug:          false,
				CalculatedMethod: calculatedMethodType,
			}
			startTime := time.Now()
			resp, err := integrate.DishSearcherClient.DishSearcherSearchByRecallField(ctx, request)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "DishSearcher"), zap.String("cost", time.Since(startTime).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "DishSearcherDowngrade")
			logger.MyDebug(ctx, traceInfo.IsDebug, "diff debug log: DishSearcherDowngrade", zap.Any("req", request), zap.Any("response", resp))
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
				traceInfo.AddErrorToTraceInfo(err)
				logkit.Error("dish search by field error", zap.Any("KeywordInfo", infos), zap.Error(err))
				metric_reporter.ReportClientRequestError(1, "DishSearcherDowngrade", "failed")
				return
			}
			metric_reporter.ReportClientRequestError(1, "DishSearcherDowngrade", "0")
			dishes := make([]*model.DishInfo, 0, len(resp.Items)*2)
			for _, item := range resp.Items {
				for _, subItem := range item.SubItems {
					dish := &model.DishInfo{
						DishId:      subItem.GetId(),
						Score:       float64(subItem.GetScore()),
						ESScore:     float64(subItem.GetScore()),
						StoreId:     item.GetId(),
						HasPicture:  subItem.GetHasPicture(),
						SalesVolume: uint32(subItem.GetSalesVolume()),
					}
					dishes = append(dishes, dish)
				}
			}
			dishRecallList[index] = dishes
		}, i, tempStoreId)
	}
	wg2.Wait()

	// 二维list展开
	nDish := 0
	if len(dishRecallList) > 0 && len(dishRecallList[0]) > 0 {
		nDish = len(dishRecallList) * len(dishRecallList[0])
	}
	dishList := make([]*model.DishInfo, 0, nDish)
	for i := range dishRecallList {
		if dishRecallList[i] != nil {
			dishList = append(dishList, dishRecallList[i]...)
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "dish recall by dish result", zap.Any("keyword", infos), zap.Any("storeIDs", storeIds), zap.Any("dish", dishList))
	return dishList
}

func getDishSearcherFilter() *o2oalgo_dishsearcher.Filter {
	filterList := make([]*o2oalgo_dishsearcher.EachFilter, 0, 3)
	filterList = append(filterList, &o2oalgo_dishsearcher.EachFilter{
		Type:      1,
		Name:      "available",
		IntValues: []int64{0},
	})
	filterList = append(filterList, &o2oalgo_dishsearcher.EachFilter{
		Type:      1,
		Name:      "listing_status",
		IntValues: []int64{0},
	})

	filter := &o2oalgo_dishsearcher.Filter{ItemFilter: filterList}
	return filter
}

func getDishSearcherSorter(limit int) *o2oalgo_dishsearcher.Sorter {
	sortList := make([]*o2oalgo_dishsearcher.EachSorter, 0, 4)
	sortList = append(sortList, &o2oalgo_dishsearcher.EachSorter{
		Name:  "score",
		Order: "desc",
	})
	sortList = append(sortList, &o2oalgo_dishsearcher.EachSorter{
		Name:  "has_picture",
		Order: "desc",
	})
	sortList = append(sortList, &o2oalgo_dishsearcher.EachSorter{
		Name:  "sales_volume",
		Order: "desc",
	})
	sortList = append(sortList, &o2oalgo_dishsearcher.EachSorter{
		Name:  "id",
		Order: "asc",
	})
	if limit == 0 {
		limit = 2
	}

	sort := &o2oalgo_dishsearcher.Sorter{
		ItemSort:   sortList,
		ItemMaxNum: int32(limit),
	}
	return sort
}
