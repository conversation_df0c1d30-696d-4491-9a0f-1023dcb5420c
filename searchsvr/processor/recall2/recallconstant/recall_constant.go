package recallconstant

const (
	HardcodeRecallIdStoreIndex                    = "1001"
	HardcodeRecallIdStoreWithDishField            = "1002"
	HardcodeRecallIdStoreCateExpand               = "1003"
	HardcodeRecallIdMainSiteStoreIndex            = "2001"
	HardcodeRecallIdMainSiteStoreWithDishField    = "2002"
	HardcodeRecallIdMainSiteDishIndex             = "2003"
	HardcodeRecallIdESDishIndex                   = "4001" // es 召菜
	HardcodeRecallIdESDishL3Category              = "4002" // es 召菜
	HardcodeRecallIdDishSearcherDishRawQuery      = "4102" // dishsearcher raw 召菜
	HardcodeRecallIdDishSearcherDishRewrite       = "4103" // dishsearcher 改写召菜
	HardcodeRecallIdDishSearcherDishWithCatalogId = "4104" // dishsearcher catalogId召菜
	HardcodeRecallIdDishSearcherDowngrade         = "4105" // dishsearcher 降级
	HardcodeRecallTypeDishSearcherDowngrade       = "DishSearcherDowngrade"
)

const (
	RecallDomainStore = "store"
	RecallDomainDish  = "dish"
)

const (
	RecallDataSourceES           = "es"
	RecallDataSourceFS           = "fs"
	RecallDataSourceVectorEngine = "vec"
)

const MaxRecallPriority = 999
