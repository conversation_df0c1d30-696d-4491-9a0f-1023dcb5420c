package recall2

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
)

func recallDishFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) (model.DishInfos, error) {
	if len(storeIds) == 0 {
		return nil, nil
	}

	// 提前获取下配置，如果没有召回配置，不需要往下走
	recallConfig := parse.GetDishRecallConfigByPipelineType(ctx, traceInfo, recallconstant.RecallDomainDish, recallconstant.RecallDataSourceES)
	if recallConfig == nil || len(recallConfig.DishRecalls) == 0 {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("recallDishFromES failed, no recall config")
		}
		return nil, nil
	}

	pt := time.Now()
	var dishInfos model.DishInfos
	defer func() {
		if e := recover(); e != nil {
			logkit.Error("recallDishFromES panic", logkit.String("recall type", "recallDishFromES"), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
			reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
				Key: "method",
				Val: "search-dish-recallDishFromES",
			})
		}

		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingMultiRecallES, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenRecallFromES, len(dishInfos))
	}()

	// 将 store_id 按照分片分组
	shardStores := make(map[int][]uint64)
	shardCnt := apollo.GetRoutingDishIndexShardCnt()
	routingMapping := apollo.GetRoutingDishIndexShardHashPosMapping()
	for _, sid := range storeIds {
		routingKey := routingMapping[int(sid)%shardCnt]
		shardStores[routingKey] = append(shardStores[routingKey], sid)
	}

	batchSize := 50
	if apollo.SearchApolloCfg.NewDishRecallBatch != 0 {
		batchSize = apollo.SearchApolloCfg.NewDishRecallBatch
	}

	var wg sync.WaitGroup
	dishRecallList := make([][]model.DishInfos, len(shardStores))

	// 计算总批次数
	totalBatches := 0
	for _, shardStoreIds := range shardStores {
		batchCount := len(shardStoreIds) / batchSize
		if len(shardStoreIds)%batchSize > 0 {
			batchCount++
		}
		totalBatches += batchCount
	}

	// 创建带缓冲的错误通道，容量为总批次数
	errs := make(chan error, totalBatches)
	errCount := 0

	// 遍历每个分片
	batchIndex := 0
	cnt := 0
	for routingKey, shardStoreIds := range shardStores {
		total := len(shardStoreIds) / batchSize
		if len(shardStoreIds)%batchSize > 0 {
			total++
		}
		cnt += total
		logkit.FromContext(ctx).Debug("BatchRecallFromEs", logkit.Int("batch", batchIndex), logkit.Int("batchSize", len(shardStoreIds)), logkit.Int("subBatchCnt", total))

		recallList := make([]model.DishInfos, total)
		for i := 0; i < total; i++ {
			var tempStoreIds []uint64
			if i+1 < total {
				tempStoreIds = shardStoreIds[i*batchSize : (i+1)*batchSize]
			} else {
				tempStoreIds = shardStoreIds[i*batchSize:]
			}

			wg.Add(1)
			goroutine.WithGo(ctx, "BatchRecallFromEs", func(params ...interface{}) {
				defer wg.Done()

				param := params[0].([]interface{})
				idx := param[0].(int)
				tempStoreId := param[1].([]uint64)
				routing := param[2].(int)

				dishList, err := doRecallDishFromES(ctx, traceInfo, tempStoreId, routing)
				if err != nil {
					traceInfo.AddErrorToTraceInfo(err)
					errs <- err
					return
				}
				recallList[idx] = dishList
			}, i, tempStoreIds, routingKey)
		}
		dishRecallList[batchIndex] = recallList
		batchIndex += 1
	}
	wg.Wait()
	close(errs) // 关闭通道

	// 统计错误数量
	for range errs {
		errCount++
	}

	// 只有当所有批次都失败时才返回错误
	if errCount > 0 && errCount == totalBatches {
		return nil, fmt.Errorf("all ES dish recall batches failed")
	}

	for _, dishList := range dishRecallList {
		if len(dishList) > 0 {
			for _, dishes := range dishList {
				if len(dishes) > 0 {
					dishInfos = append(dishInfos, dishes...)
				}
			}
		}
	}

	return dishInfos, nil
}

func doRecallDishFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64, routingKey int) (model.DishInfos, error) {
	var dishes model.DishInfos
	var err error

	if len(storeIds) == 0 {
		return dishes, nil
	}

	dishRecall := &recall.DishRecall{}
	dishRecall = dishRecall.BuildDishRecallV2(ctx, traceInfo, storeIds)
	if dishRecall == nil || len(dishRecall.DishQueries) == 0 {
		logkit.FromContext(ctx).Debug("doRecallDishFromES failed: no recall configs")
		return dishes, nil
	}

	// 强制分片
	if traceinfo.IsDishUseRouting(traceInfo, traceinfo.DishES) {
		for _, req := range dishRecall.DishQueries {
			req.Routing = fmt.Sprintf("%d", routingKey)
		}
	}

	dishesList, err := recall.RecallMultiDishesFromES(ctx, traceInfo, dishRecall.DishQueries)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("doRecallDishFromES failed")
	} else {
		// 去重merge
		dishMap := make(map[uint64]*model.DishInfo, len(dishesList)*30)
		for _, _dishes := range dishesList {
			if _dishes == nil || len(_dishes.Dishes) == 0 {
				continue
			}
			for _, dishItem := range _dishes.Dishes {
				if dishItem == nil {
					continue
				}
				// 优先级赋值
				dishItem.RecallPriority = _dishes.RecallPriority
				dishItem.DishRecallPriorities = []int{_dishes.RecallPriority}
				// 召回类型赋值
				dishItem.DishRecallType = _dishes.RecallTypeStr

				// 如果es的子查询明确有name，就用这个name来做recallId
				if len(dishItem.ESMatchedQueries) > 0 {
					for _, matchedQuery := range dishItem.ESMatchedQueries {
						dishItem.DishRecallIds = append(dishItem.DishRecallIds, fmt.Sprintf("%s_%s", _dishes.RecallId, matchedQuery))
						dishItem.DishRecallTypes = append(dishItem.DishRecallTypes, fmt.Sprintf("%s_%s", _dishes.RecallTypeStr, matchedQuery))
					}
				} else if len(_dishes.RecallId) > 0 {
					// 否则用配置化的recallId来做recallId
					dishItem.DishRecallIds = append(dishItem.DishRecallIds, _dishes.RecallId)
					dishItem.DishRecallTypes = append(dishItem.DishRecallTypes, _dishes.RecallTypeStr)
				}

				// es内部去重，重复的会合并recallIds
				if _, exists := dishMap[dishItem.DishId]; !exists {
					dishMap[dishItem.DishId] = dishItem
					dishes = append(dishes, dishItem)
				} else {
					dishMap[dishItem.DishId].DishRecallIds = append(dishMap[dishItem.DishId].DishRecallIds, dishItem.DishRecallIds...)
					dishMap[dishItem.DishId].DishRecallTypes = append(dishMap[dishItem.DishId].DishRecallTypes, dishItem.DishRecallTypes...)
					dishMap[dishItem.DishId].DishRecallPriorities = append(dishMap[dishItem.DishId].DishRecallPriorities, dishItem.RecallPriority)

					// 优先级覆盖，越低越好
					if dishMap[dishItem.DishId].RecallPriority > dishItem.RecallPriority {
						dishMap[dishItem.DishId].RecallPriority = dishItem.RecallPriority
					}
				}
			}
		}

	}
	return dishes, err
}
