package processor

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
)

func SearchPrepaidForBagPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) model.PrepaidInfos {
	pt := time.Now()
	var err error
	var prepaidInfos model.PrepaidInfos
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseNormalPipelineInternal, time.Since(pt))
		debugInfo.FillPrepaidInfos(traceInfo, prepaidInfos)
	}()
	// 前置处理
	preprocess.SearchPrepaidPipelinePreProcess(ctx, traceInfo)

	// 多路召回
	prepaidInfos, err = recall.QueryPrepaidsForBagFromES(ctx, traceInfo)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("QueryPrepaidsForBagFromES failed, return error")
		return nil
	}

	// 信息填充
	prepaidInfos = filling.PrepaidForBagFilling(ctx, traceInfo, prepaidInfos)

	// 过滤
	prepaidInfos = filter.PrepaidForBagFilter(ctx, traceInfo, prepaidInfos)

	// 排序
	prepaidInfos = prepaidInfos.SortPrepaidForBag()

	return prepaidInfos
}
