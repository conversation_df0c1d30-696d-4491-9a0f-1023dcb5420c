package processor

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/merge"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/postprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rerank"
)

func SearchPrepaidSKULandingPagePipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) ([]*model.PrepaidInfo, uint32) {
	var normalErr, fewResultErr error
	var normalPrepaids, fewResultPrepaids, finalPrepaids model.PrepaidInfos
	var fewResultIndex uint32
	var fewTraceInfo *traceinfo.TraceInfo
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseNormalAndFewResultPipeline, time.Since(pt))
		debugInfo.FillPrepaidInfos(traceInfo, finalPrepaids)
	}()
	// 前置处理
	preprocess.SearchPrepaidPipelinePreProcess(ctx, traceInfo)

	// normal 的多路召回结果作为few result 的过滤条件，放到外层调用
	normalRecallPrepaidInfos := recall.QueryPrepaidDishesFromES(ctx, traceInfo, traceInfo.QueryKeyword, []uint64{})

	wg := sync.WaitGroup{}
	wg.Add(1)
	goroutine.WithGo(ctx, "SearchPrepaidNormal", func(params ...interface{}) {
		defer wg.Done()
		traceInfo.PipelineType = traceinfo.PipelineTypeSearchPrepaidNormal
		normalPrepaids, normalErr = NormalPrepaidPipeline(ctx, traceInfo, normalRecallPrepaidInfos)
		if normalErr != nil {
			traceInfo.AddErrorToTraceInfo(normalErr)
			logkit.FromContext(ctx).Error("SearchPrepaidNormal error", logkit.Err(normalErr))
			return
		}
		normalPrepaids = filling.PrepaidItemTypeFilling(ctx, traceInfo, normalPrepaids, foodalgo_search.ItemSceneType_NormalItem)
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "SearchPrepaidFewResult", func(params ...interface{}) {
		defer wg.Done()
		fewTraceInfo = traceinfo.NewTraceInfo()
		_ = preprocess.TraceInfoClone(ctx, traceInfo, fewTraceInfo)
		fewTraceInfo.PipelineType = traceinfo.PipelineTypeSearchPrepaidFewResult
		fewTraceInfo.AbParamClient.SceneType = fewTraceInfo.PipelineType.String()
		fewTraceInfo.TraceRequest.PublishId = "FewResult_" + fewTraceInfo.TraceRequest.PublishId
		fewResultPrepaids, fewResultErr = FewResultPrepaidPipeline(ctx, fewTraceInfo, normalRecallPrepaidInfos)
		if fewResultErr != nil {
			traceInfo.AddErrorToTraceInfo(fewResultErr)
			logkit.FromContext(ctx).Error("SearchPrepaidFewResult error", logkit.Err(fewResultErr))
			return
		}
		fewResultPrepaids = filling.PrepaidItemTypeFilling(ctx, traceInfo, fewResultPrepaids, foodalgo_search.ItemSceneType_NoFewItem)
	})
	wg.Wait()

	traceInfo.MergeTraceInfo(fewTraceInfo, "FewResult")
	finalPrepaids = mergeNormalAndFewPrepaid(ctx, traceInfo, normalPrepaids, fewResultPrepaids)
	// 并标记index
	for i, s := range finalPrepaids {
		if s.ItemSceneType == foodalgo_search.ItemSceneType_NoFewItem {
			fewResultIndex = uint32(i + 1)
			break
		}
	}
	return finalPrepaids, fewResultIndex
}

func NormalPrepaidPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, normalRecallPrepaidInfos model.PrepaidInfos) (model.PrepaidInfos, error) {
	// 多路召回 normal 的多路召回结果作为fewresult 的过滤条件，放到外层调用
	//prepaidInfos := recall.QueryPrepaidDishesFromES(ctx, traceInfo, traceInfo.QueryKeyword, []uint64{})

	// 信息填充
	prepaidInfos := filling.PrepaidLandingPageFilling(ctx, traceInfo, normalRecallPrepaidInfos)

	// 过滤
	prepaidInfos = filter.PrepaidLandingPageFilter(ctx, traceInfo, prepaidInfos)

	// 融合公式计算和排序
	prepaidInfos = rank.PrepaidRank(ctx, traceInfo, prepaidInfos)

	// 重排打散
	prepaidInfos = rerank.PrepaidReRank(ctx, traceInfo, prepaidInfos)

	// ack dump
	postprocess.PostProcessorPrepaidAckDump(ctx, traceInfo, prepaidInfos)

	return prepaidInfos, nil
}

func FewResultPrepaidPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, normalRecallPrepaidInfos model.PrepaidInfos) (model.PrepaidInfos, error) {
	// 多路召回prepaid ids
	dsRecallPrepaidsList, allPrepaidIds := recall.RecallPrepaidIdsFromDataServerWithConfigs(ctx, traceInfo, normalRecallPrepaidInfos)

	// 根据prepaid ids 从ES prepaid dish索引中召回，keyword 为空
	esRecallPrepaids := recall.QueryPrepaidDishesFromES(ctx, traceInfo, "", allPrepaidIds)

	//保量、优先级融合截断
	prepaidInfos := merge.PrepaidsMergeAndTruncate(ctx, traceInfo, esRecallPrepaids, dsRecallPrepaidsList)

	// 信息填充
	prepaidInfos = filling.PrepaidLandingPageFilling(ctx, traceInfo, prepaidInfos)

	// 过滤
	prepaidInfos = filter.PrepaidLandingPageFilter(ctx, traceInfo, prepaidInfos)

	// 融合公式计算和排序
	prepaidInfos = rank.PrepaidRank(ctx, traceInfo, prepaidInfos)

	//  few result品牌打散，会受外层normal召回结果去重后的影响，需要挪到normal merge处理
	//prepaidInfos = rerank.PrepaidReRank(ctx, traceInfo, prepaidInfos)

	//// ack dump， few result 不需要 ack dump
	//postprocess.PostProcessorPrepaidAckDump(ctx, traceInfo, prepaidInfos)

	return prepaidInfos, nil
}

// 去重，合并， few result 结果再执行品牌打散
func mergeNormalAndFewPrepaid(ctx context.Context, traceInfo *traceinfo.TraceInfo, normalPrepaids, fewPrepaids []*model.PrepaidInfo) []*model.PrepaidInfo {
	if len(fewPrepaids) == 0 {
		// 如果few result 结果为空，直接返回normal结果
		return normalPrepaids
	}
	prepaids := make([]*model.PrepaidInfo, 0, len(normalPrepaids)+len(fewPrepaids))
	prepaidIdMap := make(map[uint64]bool, len(normalPrepaids))
	dishIdMap := make(map[uint64]bool, len(normalPrepaids))
	for _, p := range normalPrepaids {
		if p == nil || p.PrepaidId == 0 || p.DishId == 0 {
			continue
		}
		prepaids = append(prepaids, p)
		prepaidIdMap[p.PrepaidId] = true
		dishIdMap[p.DishId] = true
	}
	// 当normal 数量 < K 时，需要填充少无结果推荐, 默认为0，即没有normal结果才展示少结果推荐
	normalNumRecommend := abtest.GetPrepaidNormalNumForFewResult(ctx, traceInfo.AbParamClient)
	if len(prepaids) >= normalNumRecommend {
		// 不展示少结果推荐
		ext := fmt.Sprintf("normalNumRecommend: %d, normalLen: %d, fewResultLen: %d", normalNumRecommend, len(prepaids), len(fewPrepaids))
		for _, p := range fewPrepaids {
			if p == nil || p.PrepaidId == 0 || p.DishId == 0 {
				continue
			}
			traceInfo.AddFilteredStore(p.PrepaidId, traceinfo.FilteredTypeNormalNumForFewResult, ext)
		}
		return prepaids
	}
	// 去重, 合并，按照prepaid id & dish id维度去重, 然后few result 结果再执行品牌打散
	tmpFew := make([]*model.PrepaidInfo, 0, len(fewPrepaids)) // 品牌打散前
	for _, p := range fewPrepaids {
		if p == nil || p.PrepaidId == 0 || p.DishId == 0 {
			continue
		}
		if prepaidIdMap[p.PrepaidId] == true {
			traceInfo.AddFilteredStore(p.PrepaidId, traceinfo.FilteredTypeDupFewResultPrepaid, nil)
			continue
		}
		if dishIdMap[p.DishId] == true {
			traceInfo.AddFilteredStore(p.PrepaidId, traceinfo.FilteredTypeDupFewResultPrepaid, nil)
			continue
		}
		tmpFew = append(tmpFew, p)
	}
	tmpFew = rerank.BrandShufflePrepaids(ctx, traceInfo, tmpFew) // few result 去重后品牌打散
	prepaids = append(prepaids, tmpFew...)                       // 合并few result 结果到normal结果中
	return prepaids
}
