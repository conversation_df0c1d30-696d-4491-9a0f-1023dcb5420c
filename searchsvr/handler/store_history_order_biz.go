package handler

import (
	"context"
	"regexp"
	"runtime/debug"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/predefined_keyword"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/handler/historyorder"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
	"github.com/gogo/protobuf/proto"
)

var (
	defaultPageNum  = 1
	defaultPageSize = 20
)

func SearchHistoryOrders(ctx context.Context, req *foodalgo_search.SearchHistoryOrderRequest, tranReq *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchHistoryOrderResponse, traceInfo *traceinfo.TraceInfo) error {
	pt := time.Now()
	rsp.Orders = make([]*foodalgo_search.SearchHistoryOrderItem, 0)
	defer func() {
		if e := recover(); e != nil {
			logkit.Error("search es panic", logkit.String("recall type", "historyOrder"), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
			reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
				Key: "method",
				Val: "search-es-historyOrder",
			})
		}
		metric_reporter2.ReportNoResultWithScene("SearchHistoryOrders", len(rsp.Orders))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.HistoryOrderLenFinal, len(rsp.Orders))
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseHistoryOrderPipeline, time.Since(pt))
	}()

	traceInfo.PipelineType = traceinfo.GetPipelineType(traceInfo.HandlerType, tranReq)

	// parse nextId
	firstPageSize := 0
	if req.GetPageSize() > 0 {
		firstPageSize = int(req.GetPageSize())
	} else {
		firstPageSize = defaultPageSize
	}
	pageNum, pageSize, _, err := historyorder.ParseNextID(req.GetNextId(), defaultPageNum, firstPageSize)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("parse nextId failed", logkit.String("nextId", req.GetNextId()))
		pageNum = 1
		pageSize = firstPageSize
	}

	// create dsl
	keyword := normalizeKeyword(ctx, req.GetKeyword())
	esSearch := historyorder.CreateEsSearch(ctx, traceInfo, pageNum, pageSize, keyword, tranReq.GetBuyerId())

	// debug 模式下方便查看 dsl
	if traceInfo.IsDebug {
		traceInfo.PhraseOrderESDsl.Store("HardCodeHistoryOrderRecall_1", esSearch.DslString(ctx))
		traceInfo.RecallsStoreFinal = append(traceInfo.RecallsOrdersFinal, "HardCodeHistoryOrderRecall_1")
	}

	// recall
	eSClient := traceinfo.GetESClientFromPool(ctx, traceInfo, traceinfo.HistoryOrderES)
	totalHits, historyOrders, err := recall.SearchHistoryOrderES(ctx, traceInfo, eSClient, esSearch)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("SearchHistoryOrderES failed")
		return err
	}
	logkit.FromContext(ctx).Info("SearchHistoryOrderES", logkit.Uint64("totalHits", totalHits))

	// 高亮
	orders := historyorder.HighLightWords(ctx, historyOrders, keyword)

	// 分页，如果结果不足一页，就没有下次的 nextId 了
	rsp.Orders = orders
	if len(rsp.Orders) <= pageSize {
		rsp.NextId = proto.String("")
	} else {
		rsp.Orders = rsp.Orders[0:pageSize] // es 会额外多请求一个，用来做分页判断，这里要减掉
		newNextId, _ := historyorder.GenerateNextID(pageNum+1, pageSize, time.Now())
		rsp.NextId = proto.String(newNextId)
	}
	return nil
}

var spaceRegex = regexp.MustCompile(`\s+`)

func normalizeKeyword(ctx context.Context, keyword string) string {
	// 转换为小写
	keyword = strings.ToLower(keyword)
	// 去掉首尾空格
	keyword = strings.TrimSpace(keyword)
	// 去掉中间多余的空格（正则替换多个空格为单个空格）
	keyword = spaceRegex.ReplaceAllString(keyword, " ")

	// VN需要替换下预定义的char
	keyword = predefined_keyword.PredefinedKeywordDict.ReplacePredefinedKeyword(ctx, keyword)
	return keyword
}
