package handler

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
	"github.com/gogo/protobuf/proto"
)

// 新门店搜菜
func SearchPrepaidSKUForBag(ctx context.Context, rsp *foodalgo_search.SearchPrepaidSKUForBagResp, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	rsp.PrepaidSkuInfos = make([]*foodalgo_search.PrepaidSKUInfo, 0)
	var totalPrepaids []*foodalgo_search.PrepaidSKUInfo
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchPrepaidSKUForBag", len(rsp.GetPrepaidSkuInfos()))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenFinal, len(totalPrepaids))
		traceInfo.AddPhraseStoreLength(ctx, "ResultCount", len(totalPrepaids))
	}()

	// 第一页请求，不读缓存，但是下面需要写缓存
	if traceInfo.TraceRequest.PageNum > 1 && decision.IsUseResultCache(ctx, traceInfo) {
		cacheInfo, err1 := integrate.MixSearchResultCache.GetPrepaidInfoCache(ctx, traceInfo)
		if err1 != nil {
			traceInfo.AddErrorToTraceInfo(err1)
			logkit.FromContext(ctx).Error("GetPrepaidInfoCache error", logkit.Err(err1))
		} else {
			totalPrepaids = cacheInfo.GetPrepaids() // 将pb 转化为 []*model.PepaidInfo
		}
	}
	// not hit cache
	if len(totalPrepaids) == 0 {
		prepaidInfos := processor.SearchPrepaidForBagPipeline(ctx, traceInfo, debugInfo)
		totalPrepaids = buildRespPrepaids(traceInfo, prepaidInfos)

		if decision.IsUseResultCache(ctx, traceInfo) {
			cacheInfo := &foodalgo_search.SearchResultCacheInfo{
				Prepaids: totalPrepaids,
			}
			err2 := integrate.MixSearchResultCache.SetPrepaidInfoCache(ctx, traceInfo, cacheInfo)
			if err2 != nil {
				traceInfo.AddErrorToTraceInfo(err2)
				logkit.FromContext(ctx).WithError(err2).Error("SetPrepaidInfoCache failed")
			}
		}
	}

	prepaids, hasMore := cutOffPageStoresForPrepaidLandingPage(totalPrepaids, traceInfo.TraceRequest.PageNum, traceInfo.TraceRequest.PageSize)
	if hasMore {
		rsp.NextPageToken = proto.String(util.GetToken(traceInfo.TraceRequest.PublishId, traceInfo.TraceRequest.PageNum+1))
	}
	if len(prepaids) > 0 {
		rsp.PrepaidSkuInfos = prepaids
	}
	return nil
}
