package handler

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
	"github.com/gogo/protobuf/proto"
)

// 新门店搜菜
func SearchPrepaidSKULandingPage(ctx context.Context, rsp *foodalgo_search.SearchPrepaidSKULandingPageResp, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	rsp.PrepaidSkuInfos = make([]*foodalgo_search.PrepaidSKUInfo, 0)
	var totalPrepaids []*foodalgo_search.PrepaidSKUInfo
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchPrepaidSKULandingPage", len(rsp.GetPrepaidSkuInfos()))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.PrepaidLenFinal, len(totalPrepaids))
		traceInfo.AddPhraseStoreLength(ctx, "ResultCount", len(totalPrepaids))
	}()

	var fewResultIndex uint32
	//第一页请求，不读缓存，但是下面需要写缓存
	if traceInfo.TraceRequest.PageNum > 1 && decision.IsUseResultCache(ctx, traceInfo) {
		cacheInfo, err1 := integrate.MixSearchResultCache.GetPrepaidInfoCache(ctx, traceInfo)
		if err1 != nil {
			traceInfo.AddErrorToTraceInfo(err1)
			logkit.FromContext(ctx).Error("GetPrepaidInfoCache error", logkit.Err(err1))
		} else {
			totalPrepaids, fewResultIndex = cacheInfo.GetPrepaids(), cacheInfo.GetFewResultIndex() // 将pb 转化为 []*model.PrepaidInfo
		}
	}
	// not hit cache
	if len(totalPrepaids) == 0 {
		prepaidInfos, index := processor.SearchPrepaidSKULandingPagePipeline(ctx, traceInfo, debugInfo)
		fewResultIndex = index
		totalPrepaids = buildRespPrepaids(traceInfo, prepaidInfos)

		if decision.IsUseResultCache(ctx, traceInfo) {
			cacheInfo := &foodalgo_search.SearchResultCacheInfo{
				Prepaids:       totalPrepaids,
				FewResultIndex: proto.Uint32(fewResultIndex),
			}
			err2 := integrate.MixSearchResultCache.SetPrepaidInfoCache(ctx, traceInfo, cacheInfo)
			if err2 != nil {
				traceInfo.AddErrorToTraceInfo(err2)
				logkit.FromContext(ctx).WithError(err2).Error("SetPrepaidInfoCache failed")
			}
		}
	}

	prepaids, hasMore := cutOffPageStoresForPrepaidLandingPage(totalPrepaids, traceInfo.TraceRequest.PageNum, traceInfo.TraceRequest.PageSize)
	if hasMore {
		rsp.NextPageToken = proto.String(util.GetToken(traceInfo.TraceRequest.PublishId, traceInfo.TraceRequest.PageNum+1))
	}
	if len(prepaids) > 0 {
		rsp.PrepaidSkuInfos = prepaids
	}
	rsp.FewResultIndex = proto.Uint32(fewResultIndex)
	return nil
}

func buildRespPrepaids(traceInfo *traceinfo.TraceInfo, prepaidInfos []*model.PrepaidInfo) []*foodalgo_search.PrepaidSKUInfo {
	prepaids := make([]*foodalgo_search.PrepaidSKUInfo, 0, len(prepaidInfos))
	for _, s := range prepaidInfos {
		prepaid := buildRespPrepaid(traceInfo, s)
		prepaids = append(prepaids, prepaid)
	}
	return prepaids
}

func buildRespPrepaid(traceInfo *traceinfo.TraceInfo, s *model.PrepaidInfo) *foodalgo_search.PrepaidSKUInfo {
	prepaid := &foodalgo_search.PrepaidSKUInfo{}
	prepaid.PrepaidId = proto.Uint64(s.PrepaidId)
	prepaid.StoreId = proto.Uint64(s.StoreId)
	prepaid.DishId = proto.Uint64(s.DishId)
	prepaid.ItemSceneType = s.ItemSceneType.Enum()
	return prepaid
}

func cutOffPageStoresForPrepaidLandingPage(prepaids []*foodalgo_search.PrepaidSKUInfo, pageNum uint32, pageSize uint32) ([]*foodalgo_search.PrepaidSKUInfo, bool) {
	start := int(pageSize * (pageNum - 1))
	end := int(pageSize) + start
	flag := false
	if end < len(prepaids) {
		flag = true
	}
	if len(prepaids) <= start {
		return []*foodalgo_search.PrepaidSKUInfo{}, flag
	}
	if end <= len(prepaids) {
		return prepaids[start:end], flag
	}

	return prepaids[start:], flag
}
